/// Medical Profile Model for Khotwa App
/// Contains all medical information for a patient

class MedicalProfile {
  // Personal Information (Step 1)
  String? fullName;
  String? gender;
  DateTime? dateOfBirth;
  String? phoneNumber;

  // Medical Condition (Step 2)
  String? disabilityType;
  DateTime? injuryDate;
  String? medicalDetails;
  String? currentCondition;

  // Prosthetic Information (Step 3)
  bool usesProsthetic;
  String? prostheticType;
  DateTime? prostheticInstallDate;
  String? currentDifficulties;
  String? prostheticBrand;

  // Medical Attachments (Step 4)
  List<String> medicalReports;
  List<String> medicalImages;

  // Location Information (Step 5)
  String? state;
  String? city;
  String? address;
  double? latitude;
  double? longitude;
  bool shareLocation;

  // Consent and Completion (Step 6)
  bool privacyConsent;
  bool isProfileComplete;
  DateTime? completedAt;

  MedicalProfile({
    this.fullName,
    this.gender,
    this.dateOfBirth,
    this.phoneNumber,
    this.disabilityType,
    this.injuryDate,
    this.medicalDetails,
    this.currentCondition,
    this.usesProsthetic = false,
    this.prostheticType,
    this.prostheticInstallDate,
    this.currentDifficulties,
    this.prostheticBrand,
    this.medicalReports = const [],
    this.medicalImages = const [],
    this.state,
    this.city,
    this.address,
    this.latitude,
    this.longitude,
    this.shareLocation = false,
    this.privacyConsent = false,
    this.isProfileComplete = false,
    this.completedAt,
  });

  /// Convert to JSON for Supabase
  Map<String, dynamic> toJson() {
    return {
      'full_name': fullName,
      'gender': gender,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'phone_number': phoneNumber,
      'disability_type': disabilityType,
      'injury_date': injuryDate?.toIso8601String(),
      'medical_details': medicalDetails,
      'current_condition': currentCondition,
      'uses_prosthetic': usesProsthetic,
      'prosthetic_type': prostheticType,
      'prosthetic_install_date': prostheticInstallDate?.toIso8601String(),
      'current_difficulties': currentDifficulties,
      'prosthetic_brand': prostheticBrand,
      'medical_reports': medicalReports,
      'medical_images': medicalImages,
      'state': state,
      'city': city,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'share_location': shareLocation,
      'privacy_consent': privacyConsent,
      'is_profile_complete': isProfileComplete,
      'completed_at': completedAt?.toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  /// Create from JSON (from Supabase)
  factory MedicalProfile.fromJson(Map<String, dynamic> json) {
    return MedicalProfile(
      fullName: json['full_name'],
      gender: json['gender'],
      dateOfBirth: json['date_of_birth'] != null 
          ? DateTime.parse(json['date_of_birth']) 
          : null,
      phoneNumber: json['phone_number'],
      disabilityType: json['disability_type'],
      injuryDate: json['injury_date'] != null 
          ? DateTime.parse(json['injury_date']) 
          : null,
      medicalDetails: json['medical_details'],
      currentCondition: json['current_condition'],
      usesProsthetic: json['uses_prosthetic'] ?? false,
      prostheticType: json['prosthetic_type'],
      prostheticInstallDate: json['prosthetic_install_date'] != null 
          ? DateTime.parse(json['prosthetic_install_date']) 
          : null,
      currentDifficulties: json['current_difficulties'],
      prostheticBrand: json['prosthetic_brand'],
      medicalReports: List<String>.from(json['medical_reports'] ?? []),
      medicalImages: List<String>.from(json['medical_images'] ?? []),
      state: json['state'],
      city: json['city'],
      address: json['address'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      shareLocation: json['share_location'] ?? false,
      privacyConsent: json['privacy_consent'] ?? false,
      isProfileComplete: json['is_profile_complete'] ?? false,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
    );
  }

  /// Copy with method for updates
  MedicalProfile copyWith({
    String? fullName,
    String? gender,
    DateTime? dateOfBirth,
    String? phoneNumber,
    String? disabilityType,
    DateTime? injuryDate,
    String? medicalDetails,
    String? currentCondition,
    bool? usesProsthetic,
    String? prostheticType,
    DateTime? prostheticInstallDate,
    String? currentDifficulties,
    String? prostheticBrand,
    List<String>? medicalReports,
    List<String>? medicalImages,
    String? state,
    String? city,
    String? address,
    double? latitude,
    double? longitude,
    bool? shareLocation,
    bool? privacyConsent,
    bool? isProfileComplete,
    DateTime? completedAt,
  }) {
    return MedicalProfile(
      fullName: fullName ?? this.fullName,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      disabilityType: disabilityType ?? this.disabilityType,
      injuryDate: injuryDate ?? this.injuryDate,
      medicalDetails: medicalDetails ?? this.medicalDetails,
      currentCondition: currentCondition ?? this.currentCondition,
      usesProsthetic: usesProsthetic ?? this.usesProsthetic,
      prostheticType: prostheticType ?? this.prostheticType,
      prostheticInstallDate: prostheticInstallDate ?? this.prostheticInstallDate,
      currentDifficulties: currentDifficulties ?? this.currentDifficulties,
      prostheticBrand: prostheticBrand ?? this.prostheticBrand,
      medicalReports: medicalReports ?? this.medicalReports,
      medicalImages: medicalImages ?? this.medicalImages,
      state: state ?? this.state,
      city: city ?? this.city,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      shareLocation: shareLocation ?? this.shareLocation,
      privacyConsent: privacyConsent ?? this.privacyConsent,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  /// Validation methods
  bool isStep1Valid() {
    return fullName != null && 
           fullName!.isNotEmpty && 
           gender != null && 
           dateOfBirth != null && 
           phoneNumber != null && 
           phoneNumber!.isNotEmpty;
  }

  bool isStep2Valid() {
    return disabilityType != null && disabilityType!.isNotEmpty;
  }

  bool isStep3Valid() {
    if (!usesProsthetic) return true;
    return prostheticType != null && prostheticType!.isNotEmpty;
  }

  bool isStep5Valid() {
    return state != null && 
           state!.isNotEmpty && 
           city != null && 
           city!.isNotEmpty;
  }

  bool isStep6Valid() {
    return privacyConsent;
  }

  /// Get completion percentage
  double getCompletionPercentage() {
    int completedSteps = 0;
    if (isStep1Valid()) completedSteps++;
    if (isStep2Valid()) completedSteps++;
    if (isStep3Valid()) completedSteps++;
    // Step 4 is optional
    completedSteps++;
    if (isStep5Valid()) completedSteps++;
    if (isStep6Valid()) completedSteps++;
    
    return completedSteps / 6.0;
  }
}
