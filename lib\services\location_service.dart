import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

/// Location Service
/// Handles location permissions, GPS access, and address geocoding
class LocationService {
  /// Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// Check location permission status
  static Future<LocationPermission> checkPermission() async {
    return await Geolocator.checkPermission();
  }

  /// Request location permission
  static Future<LocationPermission> requestPermission() async {
    return await Geolocator.requestPermission();
  }

  /// Get current position
  static Future<Position?> getCurrentPosition() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ Location services are disabled');
        return null;
      }

      // Check permissions
      LocationPermission permission = await checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('❌ Location permissions are denied');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('❌ Location permissions are permanently denied');
        return null;
      }

      // Get current position
      debugPrint('📍 Getting current position...');
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      debugPrint(
        '📍 Position obtained: ${position.latitude}, ${position.longitude}',
      );
      return position;
    } catch (e) {
      debugPrint('❌ Error getting position: $e');
      return null;
    }
  }

  /// Get address from coordinates (Reverse Geocoding)
  static Future<Map<String, String>?> getAddressFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      debugPrint('🔍 Getting address for: $latitude, $longitude');

      List<Placemark> placemarks = await placemarkFromCoordinates(
        latitude,
        longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;
        debugPrint('🏠 Address found: ${place.toString()}');

        // Map international names to Algerian equivalents
        String? state = _mapToAlgerianState(
          place.administrativeArea ??
              place.subAdministrativeArea ??
              place.locality ??
              '',
        );

        String? city = _mapToAlgerianCity(
          place.locality ??
              place.subLocality ??
              place.subAdministrativeArea ??
              '',
          state ?? '',
        );

        return {
          'state': state ?? place.administrativeArea ?? '',
          'city': city ?? place.locality ?? '',
          'address': _buildFullAddress(place),
          'country': place.country ?? 'الجزائر',
        };
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error getting address: $e');
      return null;
    }
  }

  /// Build full address string from placemark
  static String _buildFullAddress(Placemark place) {
    List<String> addressParts = [];

    if (place.street != null && place.street!.isNotEmpty) {
      addressParts.add(place.street!);
    }
    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      addressParts.add(place.subLocality!);
    }
    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }

    return addressParts.join(', ');
  }

  /// Map international state names to Algerian state names
  static String? _mapToAlgerianState(String internationalName) {
    if (internationalName.isEmpty) return null;

    final stateMapping = {
      // English names
      'algiers': 'الجزائر',
      'oran': 'وهران',
      'constantine': 'قسنطينة',
      'setif': 'سطيف',
      'annaba': 'عنابة',
      'batna': 'باتنة',
      'biskra': 'بسكرة',
      'blida': 'البليدة',
      'bouira': 'البويرة',
      'tlemcen': 'تلمسان',
      'bejaia': 'بجاية',
      'jijel': 'جيجل',
      'skikda': 'سكيكدة',
      'guelma': 'قالمة',
      'mascara': 'معسكر',
      'ouargla': 'ورقلة',
      'adrar': 'أدرار',
      'chlef': 'الشلف',
      'laghouat': 'الأغواط',
      'oum el bouaghi': 'أم البواقي',
      'bechar': 'بشار',
      'tamanrasset': 'تمنراست',
      'tebessa': 'تبسة',
      'tiaret': 'تيارت',
      'tizi ouzou': 'تيزي وزو',
      'djelfa': 'الجلفة',
      'saida': 'سعيدة',
      'sidi bel abbes': 'سيدي بلعباس',
      'medea': 'المدية',
      'mostaganem': 'مستغانم',
      'msila': 'المسيلة',
      'el bayadh': 'البيض',
      'illizi': 'إليزي',
      'bordj bou arreridj': 'برج بوعريريج',
      'boumerdes': 'بومرداس',
      'el tarf': 'الطارف',
      'tindouf': 'تندوف',
      'tissemsilt': 'تيسمسيلت',
      'el oued': 'الوادي',
      'khenchela': 'خنشلة',
      'souk ahras': 'سوق أهراس',
      'tipaza': 'تيبازة',
      'mila': 'ميلة',
      'ain defla': 'عين الدفلى',
      'naama': 'النعامة',
      'ain temouchent': 'عين تموشنت',
      'ghardaia': 'غرداية',
      'relizane': 'غليزان',

      // French names (common in Algeria)
      'alger': 'الجزائر',
      'sétif': 'سطيف',
      'béjaïa': 'بجاية',
      'béchar': 'بشار',
      'tébessa': 'تبسة',
      'tizi-ouzou': 'تيزي وزو',
      'saïda': 'سعيدة',
      'sidi bel abbès': 'سيدي بلعباس',
      'médéa': 'المدية',
      'm\'sila': 'المسيلة',

      // Alternative spellings
      'tebessa': 'تبسة',
      'tebessa province': 'تبسة',
      'algeria': 'الجزائر',
      'algerie': 'الجزائر',
    };

    String lowerName = internationalName.toLowerCase().trim();

    // Direct match
    if (stateMapping.containsKey(lowerName)) {
      return stateMapping[lowerName];
    }

    // Partial match
    for (String key in stateMapping.keys) {
      if (lowerName.contains(key) || key.contains(lowerName)) {
        return stateMapping[key];
      }
    }

    return null;
  }

  /// Map international city names to Algerian city names
  static String? _mapToAlgerianCity(String internationalName, String state) {
    // This is a simplified mapping - in a real app, you'd have a comprehensive database
    final cityMapping = {
      'algiers': 'الجزائر العاصمة',
      'oran': 'وهران',
      'constantine': 'قسنطينة',
      'setif': 'سطيف',
      'annaba': 'عنابة',
      'batna': 'باتنة',
      'biskra': 'بسكرة',
      'blida': 'البليدة',
      'tlemcen': 'تلمسان',
      'bejaia': 'بجاية',
      'jijel': 'جيجل',
      'skikda': 'سكيكدة',
      'guelma': 'قالمة',
      'mascara': 'معسكر',
      'ouargla': 'ورقلة',
      'adrar': 'أدرار',
      'chlef': 'الشلف',
      'laghouat': 'الأغواط',
      'bechar': 'بشار',
      'tamanrasset': 'تمنراست',
      'tebessa': 'تبسة',
      'tiaret': 'تيارت',
      'tizi ouzou': 'تيزي وزو',
      'djelfa': 'الجلفة',
      'saida': 'سعيدة',
      'sidi bel abbes': 'سيدي بلعباس',
      'medea': 'المدية',
      'mostaganem': 'مستغانم',
      'msila': 'المسيلة',
    };

    String lowerName = internationalName.toLowerCase();
    return cityMapping[lowerName] ?? internationalName;
  }

  /// Get coordinates from address (Forward Geocoding)
  static Future<Position?> getCoordinatesFromAddress(String address) async {
    try {
      debugPrint('🔍 Getting coordinates for address: $address');

      List<Location> locations = await locationFromAddress(address);

      if (locations.isNotEmpty) {
        Location location = locations.first;
        debugPrint(
          '📍 Coordinates found: ${location.latitude}, ${location.longitude}',
        );

        return Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error getting coordinates: $e');
      return null;
    }
  }

  /// Open device location settings
  static Future<bool> openLocationSettings() async {
    return await Geolocator.openLocationSettings();
  }

  /// Open app settings for location permissions
  static Future<bool> openAppSettings() async {
    return await Geolocator.openAppSettings();
  }
}
