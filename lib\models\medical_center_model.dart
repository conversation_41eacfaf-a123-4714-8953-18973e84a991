import 'package:flutter/material.dart';

/// نموذج المركز الطبي
class MedicalCenter {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String phone;
  final String? emergencyPhone;
  final String type; // hospital, clinic, pharmacy, emergency
  final List<String> services;
  final double rating;
  final String? description;
  final String? website;
  final String? email;
  final Map<String, String> workingHours;
  final bool isEmergency;
  final bool isOpen24Hours;
  final String? imageUrl;
  final double? distanceFromUser; // في الكيلومترات

  const MedicalCenter({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.phone,
    this.emergencyPhone,
    required this.type,
    required this.services,
    required this.rating,
    this.description,
    this.website,
    this.email,
    required this.workingHours,
    required this.isEmergency,
    required this.isOpen24Hours,
    this.imageUrl,
    this.distanceFromUser,
  });

  MedicalCenter copyWith({
    String? id,
    String? name,
    String? address,
    double? latitude,
    double? longitude,
    String? phone,
    String? emergencyPhone,
    String? type,
    List<String>? services,
    double? rating,
    String? description,
    String? website,
    String? email,
    Map<String, String>? workingHours,
    bool? isEmergency,
    bool? isOpen24Hours,
    String? imageUrl,
    double? distanceFromUser,
  }) {
    return MedicalCenter(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      phone: phone ?? this.phone,
      emergencyPhone: emergencyPhone ?? this.emergencyPhone,
      type: type ?? this.type,
      services: services ?? this.services,
      rating: rating ?? this.rating,
      description: description ?? this.description,
      website: website ?? this.website,
      email: email ?? this.email,
      workingHours: workingHours ?? this.workingHours,
      isEmergency: isEmergency ?? this.isEmergency,
      isOpen24Hours: isOpen24Hours ?? this.isOpen24Hours,
      imageUrl: imageUrl ?? this.imageUrl,
      distanceFromUser: distanceFromUser ?? this.distanceFromUser,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'phone': phone,
      'emergencyPhone': emergencyPhone,
      'type': type,
      'services': services,
      'rating': rating,
      'description': description,
      'website': website,
      'email': email,
      'workingHours': workingHours,
      'isEmergency': isEmergency,
      'isOpen24Hours': isOpen24Hours,
      'imageUrl': imageUrl,
      'distanceFromUser': distanceFromUser,
    };
  }

  factory MedicalCenter.fromJson(Map<String, dynamic> json) {
    return MedicalCenter(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      phone: json['phone'],
      emergencyPhone: json['emergencyPhone'],
      type: json['type'],
      services: List<String>.from(json['services']),
      rating: json['rating'].toDouble(),
      description: json['description'],
      website: json['website'],
      email: json['email'],
      workingHours: Map<String, String>.from(json['workingHours']),
      isEmergency: json['isEmergency'],
      isOpen24Hours: json['isOpen24Hours'],
      imageUrl: json['imageUrl'],
      distanceFromUser: json['distanceFromUser']?.toDouble(),
    );
  }

  /// الحصول على أيقونة المركز حسب النوع
  String get typeIcon {
    switch (type) {
      case 'hospital':
        return '🏥';
      case 'clinic':
        return '🏥';
      case 'pharmacy':
        return '💊';
      case 'emergency':
        return '🚨';
      case 'dental':
        return '🦷';
      case 'laboratory':
        return '🔬';
      default:
        return '🏥';
    }
  }

  /// الحصول على اسم النوع بالعربية
  String get typeNameArabic {
    switch (type) {
      case 'hospital':
        return 'مستشفى';
      case 'clinic':
        return 'عيادة';
      case 'pharmacy':
        return 'صيدلية';
      case 'emergency':
        return 'طوارئ';
      case 'dental':
        return 'عيادة أسنان';
      case 'laboratory':
        return 'مختبر';
      default:
        return 'مركز طبي';
    }
  }

  /// التحقق من كون المركز مفتوح الآن
  bool get isCurrentlyOpen {
    if (isOpen24Hours) return true;

    final now = DateTime.now();
    final dayName = _getDayName(now.weekday);
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    final todayHours = workingHours[dayName];
    if (todayHours == null || todayHours.toLowerCase() == 'مغلق') return false;

    // تحليل أوقات العمل (مثال: "08:00-17:00")
    final parts = todayHours.split('-');
    if (parts.length != 2) return false;

    final openTime = parts[0].trim();
    final closeTime = parts[1].trim();

    return currentTime.compareTo(openTime) >= 0 &&
        currentTime.compareTo(closeTime) <= 0;
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'الاثنين';
      case 2:
        return 'الثلاثاء';
      case 3:
        return 'الأربعاء';
      case 4:
        return 'الخميس';
      case 5:
        return 'الجمعة';
      case 6:
        return 'السبت';
      case 7:
        return 'الأحد';
      default:
        return 'الأحد';
    }
  }

  /// الحصول على نص المسافة
  String get distanceText {
    if (distanceFromUser == null) return '';
    if (distanceFromUser! < 1) {
      return '${(distanceFromUser! * 1000).round()} م';
    }
    return '${distanceFromUser!.toStringAsFixed(1)} كم';
  }
}

/// أرقام الطوارئ
class EmergencyNumbers {
  static const List<EmergencyContact> contacts = [
    EmergencyContact(
      name: 'الإسعاف',
      number: '997',
      iconData: Icons.local_hospital,
      description: 'خدمة الإسعاف والطوارئ الطبية',
    ),
    EmergencyContact(
      name: 'الشرطة',
      number: '999',
      iconData: Icons.local_police,
      description: 'خدمة الشرطة والأمن العام',
    ),
    EmergencyContact(
      name: 'الإطفاء',
      number: '998',
      iconData: Icons.local_fire_department,
      description: 'خدمة الإطفاء والحماية المدنية',
    ),
    EmergencyContact(
      name: 'مركز السموم',
      number: '191',
      iconData: Icons.warning,
      description: 'مركز معلومات السموم والطوارئ',
    ),
  ];
}

/// جهة اتصال الطوارئ
class EmergencyContact {
  final String name;
  final String number;
  final IconData iconData;
  final String description;

  const EmergencyContact({
    required this.name,
    required this.number,
    required this.iconData,
    required this.description,
  });
}
