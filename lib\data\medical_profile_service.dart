import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/medical_profile_model.dart';

/// Medical Profile Service
/// Handles all database operations for medical profiles
class MedicalProfileService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get current user's medical profile
  Future<MedicalProfile?> getCurrentUserProfile() async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل دخول');
    }

    try {
      final response = await _supabase
          .from('profiles')
          .select()
          .eq('id', user.id)
          .maybeSingle();

      if (response == null) {
        return null;
      }

      return MedicalProfile.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحميل الملف الطبي: ${e.toString()}');
    }
  }

  /// Save or update medical profile
  Future<void> saveProfile(MedicalProfile profile) async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل دخول');
    }

    try {
      final profileData = profile.toJson();
      profileData['id'] = user.id;

      // Check if profile exists
      final existingProfile = await _supabase
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .maybeSingle();

      if (existingProfile != null) {
        // Update existing profile
        await _supabase.from('profiles').update(profileData).eq('id', user.id);
      } else {
        // Insert new profile
        await _supabase.from('profiles').insert(profileData);
      }
    } catch (e) {
      throw Exception('فشل في حفظ الملف الطبي: ${e.toString()}');
    }
  }

  /// Check if user has completed their medical profile
  Future<bool> isProfileComplete() async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      debugPrint('❌ No current user found');
      return false;
    }

    try {
      debugPrint('📋 Checking profile completion for user: ${user.id}');
      final response = await _supabase
          .from('profiles')
          .select('is_profile_complete')
          .eq('id', user.id)
          .maybeSingle();

      debugPrint('📋 Profile query response: $response');
      final isComplete = response?['is_profile_complete'] ?? false;
      debugPrint('📋 Profile is complete: $isComplete');
      return isComplete;
    } catch (e) {
      debugPrint('❌ Error checking profile completion: $e');
      return false;
    }
  }

  /// Upload medical file (image or PDF)
  Future<String> uploadMedicalFile(Uint8List fileBytes, String fileName) async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل دخول');
    }

    try {
      // Create unique file name
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uniqueFileName = '${user.id}_${timestamp}_$fileName';

      // Upload to Supabase Storage
      await _supabase.storage
          .from('medical-files')
          .uploadBinary('profiles/$uniqueFileName', fileBytes);

      // Get public URL
      final publicUrl = _supabase.storage
          .from('medical-files')
          .getPublicUrl('profiles/$uniqueFileName');

      return publicUrl;
    } catch (e) {
      throw Exception('فشل في رفع الملف: ${e.toString()}');
    }
  }

  /// Delete medical file
  Future<void> deleteMedicalFile(String fileUrl) async {
    try {
      // Extract file path from URL
      final uri = Uri.parse(fileUrl);
      final pathSegments = uri.pathSegments;
      final fileName = pathSegments.last;

      // Delete from Supabase Storage
      await _supabase.storage.from('medical-files').remove([
        'profiles/$fileName',
      ]);
    } catch (e) {
      throw Exception('فشل في حذف الملف: ${e.toString()}');
    }
  }

  /// Get user's medical history
  Future<List<Map<String, dynamic>>> getMedicalHistory() async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل دخول');
    }

    try {
      final response = await _supabase
          .from('appointments')
          .select('''
            *,
            specialists (
              *,
              profiles (full_name)
            ),
            medical_centers (name, address, city)
          ''')
          .eq('patient_id', user.id)
          .order('appointment_date', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('فشل في تحميل التاريخ الطبي: ${e.toString()}');
    }
  }

  /// Update profile completion status
  Future<void> markProfileAsComplete() async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل دخول');
    }

    try {
      await _supabase
          .from('profiles')
          .update({
            'is_profile_complete': true,
            'completed_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', user.id);
    } catch (e) {
      throw Exception('فشل في تحديث حالة الملف: ${e.toString()}');
    }
  }

  /// Get profile statistics
  Future<Map<String, dynamic>> getProfileStats() async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل دخول');
    }

    try {
      // Get appointments count
      final appointmentsResponse = await _supabase
          .from('appointments')
          .select('id')
          .eq('patient_id', user.id);

      // Get completed appointments count
      final completedAppointmentsResponse = await _supabase
          .from('appointments')
          .select('id')
          .eq('patient_id', user.id)
          .eq('status', 'completed');

      // Get upcoming appointments count
      final upcomingAppointmentsResponse = await _supabase
          .from('appointments')
          .select('id')
          .eq('patient_id', user.id)
          .eq('status', 'scheduled')
          .gte(
            'appointment_date',
            DateTime.now().toIso8601String().split('T')[0],
          );

      return {
        'total_appointments': appointmentsResponse.length,
        'completed_appointments': completedAppointmentsResponse.length,
        'upcoming_appointments': upcomingAppointmentsResponse.length,
        'profile_completion': await _getProfileCompletionPercentage(),
      };
    } catch (e) {
      throw Exception('فشل في تحميل إحصائيات الملف: ${e.toString()}');
    }
  }

  /// Calculate profile completion percentage
  Future<double> _getProfileCompletionPercentage() async {
    final profile = await getCurrentUserProfile();
    if (profile == null) return 0.0;

    return profile.getCompletionPercentage();
  }

  /// Search for similar medical cases (for recommendations)
  Future<List<Map<String, dynamic>>> getSimilarCases(
    String disabilityType,
  ) async {
    try {
      final response = await _supabase
          .from('profiles')
          .select('''
            disability_type,
            prosthetic_type,
            current_condition,
            state,
            city
          ''')
          .eq('disability_type', disabilityType)
          .eq('is_profile_complete', true)
          .limit(10);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('فشل في البحث عن الحالات المشابهة: ${e.toString()}');
    }
  }

  /// Get recommended specialists based on user's condition
  Future<List<Map<String, dynamic>>> getRecommendedSpecialists() async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل دخول');
    }

    try {
      final profile = await getCurrentUserProfile();
      if (profile == null || profile.disabilityType == null) {
        return [];
      }

      // Get specialists based on disability type and location
      final response = await _supabase
          .from('specialists')
          .select('''
            *,
            profiles (full_name, phone_number),
            medical_centers (name, address, city, state, rating)
          ''')
          .contains('specialization', [profile.disabilityType])
          .eq('is_available', true)
          .order('rating', ascending: false)
          .limit(5);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('فشل في تحميل المختصين المقترحين: ${e.toString()}');
    }
  }

  /// Update user location
  Future<void> updateLocation(
    double latitude,
    double longitude,
    String address,
  ) async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل دخول');
    }

    try {
      await _supabase
          .from('profiles')
          .update({
            'latitude': latitude,
            'longitude': longitude,
            'address': address,
            'share_location': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', user.id);
    } catch (e) {
      throw Exception('فشل في تحديث الموقع: ${e.toString()}');
    }
  }

  /// Get nearby medical centers
  Future<List<Map<String, dynamic>>> getNearbyMedicalCenters({
    double? latitude,
    double? longitude,
    double radiusKm = 50.0,
  }) async {
    try {
      // If no coordinates provided, get from user profile
      if (latitude == null || longitude == null) {
        final profile = await getCurrentUserProfile();
        if (profile != null &&
            profile.latitude != null &&
            profile.longitude != null) {
          latitude = profile.latitude!;
          longitude = profile.longitude!;
        } else {
          // Return all centers if no location available
          final response = await _supabase
              .from('medical_centers')
              .select()
              .eq('is_active', true)
              .order('rating', ascending: false)
              .limit(10);

          return List<Map<String, dynamic>>.from(response);
        }
      }

      // Use PostGIS functions to find nearby centers
      final response = await _supabase.rpc(
        'get_nearby_medical_centers',
        params: {
          'user_lat': latitude,
          'user_lng': longitude,
          'radius_km': radiusKm,
        },
      );

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      // Fallback to all centers if location-based search fails
      try {
        final response = await _supabase
            .from('medical_centers')
            .select()
            .eq('is_active', true)
            .order('rating', ascending: false)
            .limit(10);

        return List<Map<String, dynamic>>.from(response);
      } catch (fallbackError) {
        throw Exception(
          'فشل في تحميل المراكز الطبية: ${fallbackError.toString()}',
        );
      }
    }
  }
}
