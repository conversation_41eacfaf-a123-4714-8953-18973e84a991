import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/medical_center_model.dart';

/// خدمة المراكز الطبية
class MedicalCentersService {
  /// الحصول على المراكز الطبية القريبة
  static Future<List<MedicalCenter>> getNearbyMedicalCenters({
    Position? userPosition,
    double radiusKm = 10.0,
    String? type,
  }) async {
    try {
      // في التطبيق الحقيقي، ستكون هذه البيانات من API
      final allCenters = _getMockMedicalCenters();

      if (userPosition == null) {
        return allCenters
            .where((center) => type == null || center.type == type)
            .toList();
      }

      // حساب المسافة وتصفية المراكز
      final nearbyCenters = <MedicalCenter>[];

      for (final center in allCenters) {
        if (type != null && center.type != type) continue;

        final distance =
            Geolocator.distanceBetween(
              userPosition.latitude,
              userPosition.longitude,
              center.latitude,
              center.longitude,
            ) /
            1000; // تحويل إلى كيلومتر

        if (distance <= radiusKm) {
          nearbyCenters.add(center.copyWith(distanceFromUser: distance));
        }
      }

      // ترتيب حسب المسافة
      nearbyCenters.sort(
        (a, b) => (a.distanceFromUser ?? 0).compareTo(b.distanceFromUser ?? 0),
      );

      return nearbyCenters;
    } catch (e) {
      debugPrint('Error getting nearby medical centers: $e');
      return [];
    }
  }

  /// الاتصال برقم
  static Future<void> makePhoneCall(String phoneNumber) async {
    try {
      final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        throw 'لا يمكن الاتصال بالرقم $phoneNumber';
      }
    } catch (e) {
      debugPrint('Error making phone call: $e');
      rethrow;
    }
  }

  /// فتح الموقع في خرائط جوجل
  static Future<void> openInMaps(
    double latitude,
    double longitude,
    String name,
  ) async {
    try {
      final Uri mapsUri = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude&query_place_id=$name',
      );

      if (await canLaunchUrl(mapsUri)) {
        await launchUrl(mapsUri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح الخرائط';
      }
    } catch (e) {
      debugPrint('Error opening maps: $e');
      rethrow;
    }
  }

  /// فتح موقع ويب
  static Future<void> openWebsite(String url) async {
    try {
      final Uri webUri = Uri.parse(url);
      if (await canLaunchUrl(webUri)) {
        await launchUrl(webUri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح الموقع';
      }
    } catch (e) {
      debugPrint('Error opening website: $e');
      rethrow;
    }
  }

  /// إرسال بريد إلكتروني
  static Future<void> sendEmail(String email) async {
    try {
      final Uri emailUri = Uri(scheme: 'mailto', path: email);
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        throw 'لا يمكن إرسال بريد إلكتروني';
      }
    } catch (e) {
      debugPrint('Error sending email: $e');
      rethrow;
    }
  }

  /// الحصول على بيانات وهمية للمراكز الطبية
  static List<MedicalCenter> _getMockMedicalCenters() {
    return [
      // مستشفيات
      const MedicalCenter(
        id: '1',
        name: 'مستشفى الملك فهد الجامعي',
        address: 'الخبر، المملكة العربية السعودية',
        latitude: 26.3598,
        longitude: 50.1648,
        phone: '+966138966666',
        emergencyPhone: '+966138966999',
        type: 'hospital',
        services: ['طوارئ', 'جراحة', 'باطنة', 'أطفال', 'نساء وولادة'],
        rating: 4.5,
        description: 'مستشفى جامعي متخصص يقدم خدمات طبية شاملة',
        website: 'https://www.kfhu.edu.sa',
        email: '<EMAIL>',
        workingHours: {
          'الأحد': '24 ساعة',
          'الاثنين': '24 ساعة',
          'الثلاثاء': '24 ساعة',
          'الأربعاء': '24 ساعة',
          'الخميس': '24 ساعة',
          'الجمعة': '24 ساعة',
          'السبت': '24 ساعة',
        },
        isEmergency: true,
        isOpen24Hours: true,
      ),

      const MedicalCenter(
        id: '2',
        name: 'مستشفى الدمام المركزي',
        address: 'الدمام، المملكة العربية السعودية',
        latitude: 26.4207,
        longitude: 50.0888,
        phone: '+966138033333',
        emergencyPhone: '+966138033999',
        type: 'hospital',
        services: ['طوارئ', 'قلب', 'أعصاب', 'عظام', 'جلدية'],
        rating: 4.2,
        description: 'مستشفى حكومي يقدم خدمات طبية متنوعة',
        workingHours: {
          'الأحد': '24 ساعة',
          'الاثنين': '24 ساعة',
          'الثلاثاء': '24 ساعة',
          'الأربعاء': '24 ساعة',
          'الخميس': '24 ساعة',
          'الجمعة': '24 ساعة',
          'السبت': '24 ساعة',
        },
        isEmergency: true,
        isOpen24Hours: true,
      ),

      // عيادات
      const MedicalCenter(
        id: '3',
        name: 'عيادات الرعاية الأولية',
        address: 'حي الفيصلية، الدمام',
        latitude: 26.4282,
        longitude: 50.1040,
        phone: '+966138123456',
        type: 'clinic',
        services: ['طب عام', 'أطفال', 'نساء', 'أسنان'],
        rating: 4.0,
        description: 'عيادات متخصصة في الرعاية الأولية',
        workingHours: {
          'الأحد': '08:00-20:00',
          'الاثنين': '08:00-20:00',
          'الثلاثاء': '08:00-20:00',
          'الأربعاء': '08:00-20:00',
          'الخميس': '08:00-20:00',
          'الجمعة': 'مغلق',
          'السبت': '08:00-20:00',
        },
        isEmergency: false,
        isOpen24Hours: false,
      ),

      // صيدليات
      const MedicalCenter(
        id: '4',
        name: 'صيدلية النهدي',
        address: 'شارع الملك فهد، الخبر',
        latitude: 26.3496,
        longitude: 50.1663,
        phone: '+966138987654',
        type: 'pharmacy',
        services: ['أدوية', 'مستحضرات تجميل', 'فيتامينات', 'أجهزة طبية'],
        rating: 4.3,
        description: 'صيدلية شاملة تقدم جميع الأدوية والمستحضرات الطبية',
        website: 'https://www.nahdi.sa',
        workingHours: {
          'الأحد': '24 ساعة',
          'الاثنين': '24 ساعة',
          'الثلاثاء': '24 ساعة',
          'الأربعاء': '24 ساعة',
          'الخميس': '24 ساعة',
          'الجمعة': '24 ساعة',
          'السبت': '24 ساعة',
        },
        isEmergency: false,
        isOpen24Hours: true,
      ),

      // مختبرات
      const MedicalCenter(
        id: '5',
        name: 'مختبر البرج الطبي',
        address: 'حي الشاطئ، الدمام',
        latitude: 26.4456,
        longitude: 50.1063,
        phone: '+966138555777',
        type: 'laboratory',
        services: ['تحاليل دم', 'أشعة', 'تحاليل بول', 'فحوصات شاملة'],
        rating: 4.4,
        description: 'مختبر طبي متقدم يقدم جميع أنواع التحاليل والفحوصات',
        website: 'https://www.alborg.com.sa',
        workingHours: {
          'الأحد': '06:00-22:00',
          'الاثنين': '06:00-22:00',
          'الثلاثاء': '06:00-22:00',
          'الأربعاء': '06:00-22:00',
          'الخميس': '06:00-22:00',
          'الجمعة': '14:00-22:00',
          'السبت': '06:00-22:00',
        },
        isEmergency: false,
        isOpen24Hours: false,
      ),
    ];
  }
}
