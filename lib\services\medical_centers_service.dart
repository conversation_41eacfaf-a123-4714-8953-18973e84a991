import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/medical_center_model.dart';

/// خدمة المراكز الطبية الجزائرية
class MedicalCentersService {
  static final SupabaseClient _supabase = Supabase.instance.client;

  /// الحصول على جميع المراكز الطبية (للقائمة)
  static Future<List<MedicalCenter>> getAllMedicalCenters({
    String? type,
    Position? userPosition,
  }) async {
    try {
      List<Map<String, dynamic>> response;

      if (type != null && type != 'all') {
        response = await _supabase
            .from('medical_centers')
            .select()
            .eq('is_active', true)
            .eq('type', type)
            .order('rating', ascending: false)
            .limit(50);
      } else {
        response = await _supabase
            .from('medical_centers')
            .select()
            .eq('is_active', true)
            .order('rating', ascending: false)
            .limit(50);
      }

      return _convertToMedicalCenters(response, userPosition);
    } catch (e) {
      debugPrint('Error getting all medical centers: $e');
      return [];
    }
  }

  /// الحصول على المراكز الطبية القريبة (للخريطة)
  static Future<List<MedicalCenter>> getNearbyMedicalCenters({
    Position? userPosition,
    double radiusKm = 20.0,
    String? type,
  }) async {
    try {
      List<Map<String, dynamic>> response;

      if (userPosition != null) {
        // استخدام دالة البحث الجغرافي
        response = await _supabase.rpc(
          'get_nearby_medical_centers',
          params: {
            'user_lat': userPosition.latitude,
            'user_lng': userPosition.longitude,
            'radius_km': radiusKm,
          },
        );
      } else {
        // الحصول على جميع المراكز إذا لم يكن هناك موقع
        response = await _supabase
            .from('medical_centers')
            .select()
            .eq('is_active', true)
            .order('rating', ascending: false)
            .limit(20);
      }

      // تصفية حسب النوع إذا تم تحديده
      if (type != null && type != 'all') {
        response = response.where((center) => center['type'] == type).toList();
      }

      return _convertToMedicalCenters(response, userPosition);
    } catch (e) {
      debugPrint('Error getting nearby medical centers: $e');
      // في حالة الخطأ، إرجاع قائمة فارغة
      return [];
    }
  }

  /// الاتصال برقم
  static Future<void> makePhoneCall(String phoneNumber) async {
    try {
      final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        throw 'لا يمكن الاتصال بالرقم $phoneNumber';
      }
    } catch (e) {
      debugPrint('Error making phone call: $e');
      rethrow;
    }
  }

  /// فتح الموقع في خرائط جوجل
  static Future<void> openInMaps(
    double latitude,
    double longitude,
    String name,
  ) async {
    try {
      final Uri mapsUri = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude&query_place_id=$name',
      );

      if (await canLaunchUrl(mapsUri)) {
        await launchUrl(mapsUri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح الخرائط';
      }
    } catch (e) {
      debugPrint('Error opening maps: $e');
      rethrow;
    }
  }

  /// فتح موقع ويب
  static Future<void> openWebsite(String url) async {
    try {
      final Uri webUri = Uri.parse(url);
      if (await canLaunchUrl(webUri)) {
        await launchUrl(webUri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح الموقع';
      }
    } catch (e) {
      debugPrint('Error opening website: $e');
      rethrow;
    }
  }

  /// إرسال بريد إلكتروني
  static Future<void> sendEmail(String email) async {
    try {
      final Uri emailUri = Uri(scheme: 'mailto', path: email);
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        throw 'لا يمكن إرسال بريد إلكتروني';
      }
    } catch (e) {
      debugPrint('Error sending email: $e');
      rethrow;
    }
  }

  /// تحويل البيانات من قاعدة البيانات إلى نماذج
  static List<MedicalCenter> _convertToMedicalCenters(
    List<Map<String, dynamic>> response,
    Position? userPosition,
  ) {
    return response.map((data) {
      return MedicalCenter(
        id: data['id']?.toString() ?? '',
        name: data['name_ar']?.toString() ?? data['name']?.toString() ?? '',
        address: data['address']?.toString() ?? '',
        latitude: _parseDouble(data['latitude']),
        longitude: _parseDouble(data['longitude']),
        phone: data['phone']?.toString() ?? '',
        emergencyPhone: data['emergency_phone']?.toString(),
        type: data['type']?.toString() ?? '',
        services: _parseStringList(data['services']),
        rating: _parseDouble(data['rating']),
        description: data['description']?.toString() ?? '',
        website: data['website']?.toString(),
        email: data['email']?.toString(),
        workingHours: _parseWorkingHours(data['working_hours']),
        isEmergency: data['is_emergency'] == true,
        isOpen24Hours: data['is_open_24_hours'] == true,
        distanceFromUser: userPosition != null
            ? _parseDouble(data['distance_km'])
            : null,
      );
    }).toList();
  }

  /// دوال مساعدة لتحويل البيانات
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }

  static Map<String, String> _parseWorkingHours(dynamic value) {
    if (value == null) return {};
    if (value is Map) {
      return Map<String, String>.from(value);
    }
    return {};
  }
}
