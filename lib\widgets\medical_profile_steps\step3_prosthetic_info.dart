import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/medical_profile_provider.dart';
import '../../theme/app_styles.dart';

/// Step 3: Prosthetic Information
/// Collects information about prosthetic devices usage
class Step3ProstheticInfo extends StatefulWidget {
  const Step3ProstheticInfo({super.key});

  @override
  State<Step3ProstheticInfo> createState() => _Step3ProstheticInfoState();
}

class _Step3ProstheticInfoState extends State<Step3ProstheticInfo> {
  final _currentDifficultiesController = TextEditingController();
  final _prostheticBrandController = TextEditingController();

  bool _usesProsthetic = false;
  String? _selectedProstheticType;
  DateTime? _selectedInstallDate;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    final provider = context.read<MedicalProfileProvider>();
    final profile = provider.profile;

    _usesProsthetic = profile.usesProsthetic;
    _selectedProstheticType = profile.prostheticType;
    _selectedInstallDate = profile.prostheticInstallDate;
    _currentDifficultiesController.text = profile.currentDifficulties ?? '';
    _prostheticBrandController.text = profile.prostheticBrand ?? '';
  }

  @override
  void dispose() {
    _currentDifficultiesController.dispose();
    _prostheticBrandController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildUsesProstheticField(),
            if (_usesProsthetic) ...[
              const SizedBox(height: AppDimensions.marginLarge),
              _buildProstheticTypeField(),
              const SizedBox(height: AppDimensions.marginLarge),
              _buildInstallDateField(),
              const SizedBox(height: AppDimensions.marginLarge),
              _buildProstheticBrandField(),
              const SizedBox(height: AppDimensions.marginLarge),
              _buildCurrentDifficultiesField(),
            ],
            const SizedBox(height: AppDimensions.marginXLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.accessibility_new_outlined,
            color: AppColors.success,
            size: AppDimensions.iconMedium,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Text(
              'معلومات الأطراف الاصطناعية تساعدنا في تقديم النصائح والخدمات المتخصصة.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.success,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsesProstheticField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'هل تستخدم طرفاً اصطناعياً أو جهازاً مساعداً؟',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginMedium),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            color: AppColors.surface,
          ),
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _usesProsthetic = true;
                    });
                    _updateProstheticInfo();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingMedium,
                    ),
                    decoration: BoxDecoration(
                      color: _usesProsthetic
                          ? AppColors.primary
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusMedium,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: _usesProsthetic
                              ? AppColors.textOnPrimary
                              : AppColors.textSecondary,
                          size: AppDimensions.iconSmall,
                        ),
                        const SizedBox(width: AppDimensions.marginSmall),
                        Text(
                          'نعم، أستخدم',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: _usesProsthetic
                                ? AppColors.textOnPrimary
                                : AppColors.textSecondary,
                            fontWeight: _usesProsthetic
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _usesProsthetic = false;
                      _selectedProstheticType = null;
                      _selectedInstallDate = null;
                      _currentDifficultiesController.clear();
                      _prostheticBrandController.clear();
                    });
                    _updateProstheticInfo();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingMedium,
                    ),
                    decoration: BoxDecoration(
                      color: !_usesProsthetic
                          ? AppColors.primary
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusMedium,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.cancel_outlined,
                          color: !_usesProsthetic
                              ? AppColors.textOnPrimary
                              : AppColors.textSecondary,
                          size: AppDimensions.iconSmall,
                        ),
                        const SizedBox(width: AppDimensions.marginSmall),
                        Text(
                          'لا، لا أستخدم',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: !_usesProsthetic
                                ? AppColors.textOnPrimary
                                : AppColors.textSecondary,
                            fontWeight: !_usesProsthetic
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProstheticTypeField() {
    final provider = context.read<MedicalProfileProvider>();
    final prostheticTypes = provider.getProstheticTypes();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الطرف الاصطناعي أو الجهاز المساعد *',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            color: AppColors.surface,
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedProstheticType,
            decoration: const InputDecoration(
              prefixIcon: Icon(Icons.build_outlined),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingMedium,
                vertical: AppDimensions.paddingMedium,
              ),
            ),
            hint: const Text('اختر نوع الطرف الاصطناعي'),
            items: prostheticTypes.map((type) {
              return DropdownMenuItem<String>(
                value: type,
                child: Text(type, style: AppTextStyles.bodyMedium),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedProstheticType = value;
              });
              _updateProstheticInfo();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildInstallDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تاريخ التركيب (اختياري)',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        GestureDetector(
          onTap: _selectInstallDate,
          child: Container(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.border),
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              color: AppColors.surface,
            ),
            child: Row(
              children: [
                Icon(
                  Icons.schedule_outlined,
                  color: AppColors.textSecondary,
                  size: AppDimensions.iconMedium,
                ),
                const SizedBox(width: AppDimensions.marginMedium),
                Expanded(
                  child: Text(
                    _selectedInstallDate != null
                        ? '${_selectedInstallDate!.day}/${_selectedInstallDate!.month}/${_selectedInstallDate!.year}'
                        : 'اختر تاريخ التركيب (اختياري)',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: _selectedInstallDate != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                  ),
                ),
                Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProstheticBrandField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الشركة المصنعة أو النوع (اختياري)',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        TextFormField(
          controller: _prostheticBrandController,
          textDirection: TextDirection.rtl,
          decoration: InputDecoration(
            hintText: 'مثال: Ottobock، Össur، أو أي معلومات أخرى',
            prefixIcon: const Icon(Icons.business_outlined),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          onChanged: (_) => _updateProstheticInfo(),
        ),
      ],
    );
  }

  Widget _buildCurrentDifficultiesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الصعوبات الحالية أو التحسينات المطلوبة (اختياري)',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        TextFormField(
          controller: _currentDifficultiesController,
          textDirection: TextDirection.rtl,
          maxLines: 3,
          decoration: InputDecoration(
            hintText:
                'اكتب عن أي صعوبات تواجهها مع الطرف الاصطناعي أو التحسينات التي تحتاجها...',
            prefixIcon: const Padding(
              padding: EdgeInsets.only(bottom: 40),
              child: Icon(Icons.feedback_outlined),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            filled: true,
            fillColor: AppColors.surface,
            alignLabelWithHint: true,
          ),
          onChanged: (_) => _updateProstheticInfo(),
        ),
      ],
    );
  }

  Future<void> _selectInstallDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedInstallDate ??
          DateTime.now().subtract(const Duration(days: 365)),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 50)),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primary),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedInstallDate) {
      setState(() {
        _selectedInstallDate = picked;
      });
      _updateProstheticInfo();
    }
  }

  void _updateProstheticInfo() {
    final provider = context.read<MedicalProfileProvider>();
    provider.updateProstheticInfo(
      usesProsthetic: _usesProsthetic,
      prostheticType: _selectedProstheticType,
      prostheticInstallDate: _selectedInstallDate,
      currentDifficulties: _currentDifficultiesController.text.trim(),
      prostheticBrand: _prostheticBrandController.text.trim(),
    );
  }
}
