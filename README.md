# khotwa

# خطوة - Khotwa 🦾

**تطبيق ذكي وإنساني لدعم ذوي الإعاقة الحركية وربطهم بالمراكز المتخصصة**

خطوة هو تطبيق Flutter مصمم خصيصاً لدعم وتمكين الأشخاص ذوي الإعاقة الحركية والمصابين الذين يحتاجون إلى خدمات علاجية وتأهيلية، من خلال ربطهم بالمراكز المتخصصة والأطباء المعتمدين بطريقة رقمية بسيطة وآمنة.

## 🎯 أهداف التطبيق

- تسهيل الوصول إلى مراكز العلاج والتأهيل الحركي
- ربط المرضى بالمختصين المناسبين حسب الموقع ونوع الخدمة
- توفير محتوى تثقيفي ونفسي داعم للمستخدمين وذويهم
- تقليل العوائق التقنية والإدارية أمام المصابين المحتاجين لأطراف صناعية
- تسريع عملية الحجز والتواصل مع الجهات المختصة

## 👥 الفئات المستفيدة

- الأفراد ذوو الإعاقة الحركية (ذوو الطرف المبتور – محدودو الحركة)
- أولياء الأمور أو الأقارب الباحثون عن رعاية مناسبة
- المراكز الطبية المختصة (أطراف اصطناعية – علاج فيزيائي)
- الأطباء والمعالجون المعتمدون

## 🛠️ التقنيات المستخدمة

### Flutter SDK & Tools
- **Flutter Version**: 3.32.2 (Channel stable)
- **Dart Version**: 3.8.1
- **DevTools Version**: 2.45.1

### Android Development
- **Android SDK Version**: 35.0.1
- **Build Tools**: 35.0.1
- **Target SDK**: API Level 35 (Android 15)
- **Min SDK**: API Level 21 (Android 5.0)
- **Java Version**: OpenJDK Runtime Environment 21.0.6
- **Android Studio**: 2024.3.2

### Core Dependencies
- **supabase_flutter**: ^2.9.1 - Authentication and backend services
- **provider**: ^6.1.5 - State management
- **flutter_localizations**: SDK - RTL and Arabic language support
- **video_player**: ^2.10.0 - Welcome screen video background
- **email_validator**: ^3.0.0 - Form validation
- **shared_preferences**: ^2.5.3 - Local storage

### UI & Icons
- **flutter_vector_icons**: ^2.0.0 - Vector icons
- **line_icons**: ^2.0.3 - Line style icons
- **icons_plus**: ^5.0.0 - Additional icon sets
- **flutter_spinkit**: ^5.2.1 - Loading animations

## 📁 هيكل المشروع

```
lib/
├── config/          # إعدادات التطبيق المركزية
│   └── app_config.dart
├── theme/           # نظام التصميم الموحد
│   ├── app_styles.dart
│   └── app_icons.dart
├── models/          # نماذج البيانات
├── screens/         # شاشات التطبيق
├── widgets/         # المكونات القابلة لإعادة الاستخدام
├── data/            # خدمات البيانات
├── providers/       # إدارة الحالة
└── main.dart        # نقطة البداية

assets/
├── data/            # ملفات JSON للمحتوى الثابت
├── fonts/           # خطوط عربية (تجوال، القاهرة)
├── images/          # الصور والأيقونات
└── videos/          # فيديوهات الخلفية
```

## 🚀 البدء في التطوير

### المتطلبات الأساسية
1. Flutter SDK 3.32.2 أو أحدث
2. Android Studio 2024.3.2 أو أحدث
3. VS Code مع إضافة Flutter
4. Git للتحكم في الإصدارات

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd Khotwa
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **إعداد Supabase**
   - إنشاء مشروع جديد في [Supabase](https://supabase.com)
   - نسخ URL و Anon Key
   - تحديث `lib/config/app_config.dart` بالمعلومات الصحيحة

4. **تشغيل التطبيق**
```bash
flutter run
```

## 🔧 إعداد البيئة التطويرية

### Android Configuration
```bash
# التحقق من إعداد Android
flutter doctor -v

# إعداد Android SDK
flutter config --android-sdk /path/to/android/sdk

# إعداد Java JDK
flutter config --jdk-dir /path/to/jdk
```

### VS Code Extensions
- Flutter
- Dart
- Arabic Language Pack
- GitLens
- Bracket Pair Colorizer

## 📱 ميزات نظام المصادقة

### شاشة الترحيب
- خلفية فيديو تفاعلية
- أزرار "ابدأ" و "حول التطبيق"
- دعم كامل للتخطيط من اليمين لليسار (RTL)

### تسجيل حساب جديد
- مخصص للمرضى فقط
- حقول: الاسم الكامل، البريد الإلكتروني، كلمة المرور
- التحقق من البريد الإلكتروني عبر Supabase

### تسجيل الدخول
- البريد الإلكتروني وكلمة المرور
- رابط "نسيت كلمة المرور؟"
- معالجة أخطاء شاملة

### استعادة كلمة المرور
- إدخال البريد الإلكتروني
- إرسال رابط إعادة التعيين

## 🎨 نظام التصميم

### الألوان
- **الأساسي**: أخضر (#2E7D32) - يرمز للنمو والشفاء
- **الثانوي**: أزرق (#1976D2) - يرمز للثقة والموثوقية
- **المميز**: برتقالي (#FF9800) - يرمز للدفء والتشجيع

### الخطوط
- **تجوال (Tajawal)**: الخط الأساسي للنصوص العربية
- **القاهرة (Cairo)**: خط ثانوي للعناوين
- دعم كامل للأوزان: خفيف، عادي، متوسط، عريض

### الأبعاد
- نظام متسق للمسافات والحواف
- تصميم متجاوب يدعم الهواتف والأجهزة اللوحية
- دعم كامل للتخطيط RTL

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/

# اختبار الأداء
flutter test --coverage
```

### أنواع الاختبارات
- اختبارات الوحدة للمنطق التجاري
- اختبارات الواجهة للمكونات
- اختبارات التكامل للتدفقات الكاملة
- اختبارات الأداء والذاكرة

## 📋 قائمة المهام الحالية

- [x] إعداد هيكل المشروع الأساسي
- [x] تكوين التبعيات والحزم
- [x] إنشاء نظام التصميم الموحد
- [ ] إعداد خدمة Supabase للمصادقة
- [ ] تطوير شاشة الترحيب
- [ ] تطوير شاشة تسجيل حساب جديد
- [ ] تطوير شاشة تسجيل الدخول
- [ ] تطوير شاشة استعادة كلمة المرور
- [ ] إنشاء مكون الشاشة الأساسية
- [ ] إعداد هيكل الأصول والبيانات
- [ ] اختبار وتحقق من التدفق الكامل

## 🤝 المساهمة

### إرشادات التطوير
1. اتباع معايير الكود المحددة في `Guidelines`
2. استخدام `snake_case` للملفات و `CamelCase` للفئات
3. كتابة تعليقات باللغة العربية للوضوح
4. اختبار جميع التغييرات قبل الدفع
5. اتباع نمط Git Conventional Commits

### فروع Git
- `main`: الفرع الرئيسي المستقر
- `develop`: فرع التطوير النشط
- `feature/*`: فروع الميزات الجديدة
- `bugfix/*`: فروع إصلاح الأخطاء

## 📞 الدعم والتواصل

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراجعة الوثائق في مجلد `docs/`
- التواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع محمي بحقوق الطبع والنشر. جميع الحقوق محفوظة لفريق تطوير تطبيق خطوة.

---

**آخر تحديث**: يوليو 2025
**إصدار التطبيق**: 1.0.0
**إصدار الوثائق**: 1.0.0
