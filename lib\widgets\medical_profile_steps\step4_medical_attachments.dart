import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';

/// Step 4: Medical Attachments (Optional)
/// Allows users to upload medical reports and images
class Step4MedicalAttachments extends StatefulWidget {
  const Step4MedicalAttachments({super.key});

  @override
  State<Step4MedicalAttachments> createState() =>
      _Step4MedicalAttachmentsState();
}

class _Step4MedicalAttachmentsState extends State<Step4MedicalAttachments> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildUploadSection(),
            const SizedBox(height: AppDimensions.marginXLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.upload_file_outlined,
            color: AppColors.warning,
            size: AppDimensions.iconMedium,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'رفع المرفقات الطبية (اختياري)',
                  style: AppTextStyles.labelLarge.copyWith(
                    color: AppColors.warning,
                  ),
                ),
                const SizedBox(height: AppDimensions.marginSmall),
                Text(
                  'يمكنك رفع التقارير الطبية أو الصور لاحقاً من خلال الملف الشخصي.',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.warning,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadSection() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppDimensions.paddingXLarge),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            border: Border.all(
              color: AppColors.border,
              style: BorderStyle.solid,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 64,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: AppDimensions.marginMedium),
              Text(
                'رفع الملفات الطبية',
                style: AppTextStyles.headline4.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppDimensions.marginSmall),
              Text(
                'يمكنك تخطي هذه الخطوة وإضافة الملفات لاحقاً',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.marginLarge),
              OutlinedButton.icon(
                onPressed: () {
                  // TODO: Implement file upload
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('سيتم إضافة هذه الميزة قريباً'),
                    ),
                  );
                },
                icon: const Icon(Icons.add_outlined),
                label: const Text('إضافة ملفات'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: BorderSide(color: AppColors.primary),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
