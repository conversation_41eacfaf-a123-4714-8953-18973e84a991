import 'package:flutter/foundation.dart';
import '../models/medical_profile_model.dart';
import '../data/medical_profile_service.dart';
import 'auth_provider.dart';

/// Medical Profile Provider
/// Manages the state of medical profile data during the wizard process
class MedicalProfileProvider extends ChangeNotifier {
  final MedicalProfileService _service = MedicalProfileService();

  MedicalProfile _profile = MedicalProfile();
  bool _isLoading = false;
  String? _error;

  // Getters
  MedicalProfile get profile => _profile;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize with existing profile data if available
  Future<void> initialize() async {
    _setLoading(true);
    try {
      final existingProfile = await _service.getCurrentUserProfile();
      if (existingProfile != null) {
        _profile = existingProfile;
      }
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل البيانات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Update personal information (Step 1)
  void updatePersonalInfo({
    String? fullName,
    String? gender,
    DateTime? dateOfBirth,
    String? phoneNumber,
  }) {
    _profile = _profile.copyWith(
      fullName: fullName,
      gender: gender,
      dateOfBirth: dateOfBirth,
      phoneNumber: phoneNumber,
    );
    notifyListeners();
  }

  /// Update medical condition (Step 2)
  void updateMedicalCondition({
    String? disabilityType,
    DateTime? injuryDate,
    String? medicalDetails,
    String? currentCondition,
  }) {
    _profile = _profile.copyWith(
      disabilityType: disabilityType,
      injuryDate: injuryDate,
      medicalDetails: medicalDetails,
      currentCondition: currentCondition,
    );
    notifyListeners();
  }

  /// Update prosthetic information (Step 3)
  void updateProstheticInfo({
    bool? usesProsthetic,
    String? prostheticType,
    DateTime? prostheticInstallDate,
    String? currentDifficulties,
    String? prostheticBrand,
  }) {
    _profile = _profile.copyWith(
      usesProsthetic: usesProsthetic,
      prostheticType: prostheticType,
      prostheticInstallDate: prostheticInstallDate,
      currentDifficulties: currentDifficulties,
      prostheticBrand: prostheticBrand,
    );
    notifyListeners();
  }

  /// Update medical attachments (Step 4)
  void updateMedicalAttachments({
    List<String>? medicalReports,
    List<String>? medicalImages,
  }) {
    _profile = _profile.copyWith(
      medicalReports: medicalReports,
      medicalImages: medicalImages,
    );
    notifyListeners();
  }

  /// Add medical report
  void addMedicalReport(String reportUrl) {
    final updatedReports = List<String>.from(_profile.medicalReports);
    updatedReports.add(reportUrl);
    _profile = _profile.copyWith(medicalReports: updatedReports);
    notifyListeners();
  }

  /// Remove medical report
  void removeMedicalReport(String reportUrl) {
    final updatedReports = List<String>.from(_profile.medicalReports);
    updatedReports.remove(reportUrl);
    _profile = _profile.copyWith(medicalReports: updatedReports);
    notifyListeners();
  }

  /// Add medical image
  void addMedicalImage(String imageUrl) {
    final updatedImages = List<String>.from(_profile.medicalImages);
    updatedImages.add(imageUrl);
    _profile = _profile.copyWith(medicalImages: updatedImages);
    notifyListeners();
  }

  /// Remove medical image
  void removeMedicalImage(String imageUrl) {
    final updatedImages = List<String>.from(_profile.medicalImages);
    updatedImages.remove(imageUrl);
    _profile = _profile.copyWith(medicalImages: updatedImages);
    notifyListeners();
  }

  /// Update location information (Step 5)
  void updateLocationInfo({
    String? state,
    String? city,
    String? address,
    double? latitude,
    double? longitude,
    bool? shareLocation,
  }) {
    _profile = _profile.copyWith(
      state: state,
      city: city,
      address: address,
      latitude: latitude,
      longitude: longitude,
      shareLocation: shareLocation,
    );
    notifyListeners();
  }

  /// Update consent (Step 6)
  void updateConsent({bool? privacyConsent}) {
    _profile = _profile.copyWith(privacyConsent: privacyConsent);
    notifyListeners();
  }

  /// Complete the profile and save to database
  Future<void> completeProfile() async {
    _setLoading(true);
    try {
      // Mark profile as complete
      _profile = _profile.copyWith(
        isProfileComplete: true,
        completedAt: DateTime.now(),
      );

      // Save to database
      await _service.saveProfile(_profile);

      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في حفظ الملف الطبي: ${e.toString()}');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Save current progress (for auto-save functionality)
  Future<void> saveProgress() async {
    try {
      await _service.saveProfile(_profile);
    } catch (e) {
      // Silent fail for auto-save
      debugPrint('Auto-save failed: $e');
    }
  }

  /// Reset profile data
  void resetProfile() {
    _profile = MedicalProfile();
    _clearError();
    notifyListeners();
  }

  /// Validation helpers
  bool isStep1Valid() => _profile.isStep1Valid();
  bool isStep2Valid() => _profile.isStep2Valid();
  bool isStep3Valid() => _profile.isStep3Valid();
  bool isStep5Valid() => _profile.isStep5Valid();
  bool isStep6Valid() => _profile.isStep6Valid();

  double getCompletionPercentage() => _profile.getCompletionPercentage();

  /// Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Get available disability types
  List<String> getDisabilityTypes() {
    return [
      'بتر في الساق',
      'بتر في الذراع',
      'بتر في اليد',
      'بتر في القدم',
      'شلل في الأطراف السفلية',
      'شلل في الأطراف العلوية',
      'ضعف في العضلات',
      'تشوه خلقي',
      'إصابة في العمود الفقري',
      'أخرى',
    ];
  }

  /// Get available prosthetic types
  List<String> getProstheticTypes() {
    return [
      'طرف اصطناعي للساق',
      'طرف اصطناعي للذراع',
      'طرف اصطناعي لليد',
      'طرف اصطناعي للقدم',
      'جهاز مساعد للمشي',
      'كرسي متحرك',
      'عكازات',
      'أخرى',
    ];
  }

  /// Get available states in Algeria (Wilayas)
  List<String> getAlgerianStates() {
    return [
      'أدرار',
      'الشلف',
      'الأغواط',
      'أم البواقي',
      'باتنة',
      'بجاية',
      'بسكرة',
      'بشار',
      'البليدة',
      'البويرة',
      'تمنراست',
      'تبسة',
      'تلمسان',
      'تيارت',
      'تيزي وزو',
      'الجزائر',
      'الجلفة',
      'جيجل',
      'سطيف',
      'سعيدة',
      'سكيكدة',
      'سيدي بلعباس',
      'عنابة',
      'قالمة',
      'قسنطينة',
      'المدية',
      'مستغانم',
      'المسيلة',
      'معسكر',
      'ورقلة',
      'وهران',
      'البيض',
      'إليزي',
      'برج بوعريريج',
      'بومرداس',
      'الطارف',
      'تندوف',
      'تيسمسيلت',
      'الوادي',
      'خنشلة',
      'سوق أهراس',
      'تيبازة',
      'ميلة',
      'عين الدفلى',
      'النعامة',
      'عين تموشنت',
      'غرداية',
      'غليزان',
    ];
  }

  /// Get cities for a specific Algerian state
  List<String> getCitiesForState(String state) {
    final citiesMap = {
      'الجزائر': [
        'الجزائر العاصمة',
        'الحراش',
        'دار البيضاء',
        'الرويبة',
        'زرالدة',
        'شراقة',
      ],
      'وهران': [
        'وهران',
        'السانيا',
        'بئر الجير',
        'حاسي بونيف',
        'أرزيو',
        'عين الترك',
      ],
      'قسنطينة': [
        'قسنطينة',
        'الخروب',
        'حامة بوزيان',
        'ديدوش مراد',
        'زيغود يوسف',
      ],
      'سطيف': ['سطيف', 'العلمة', 'عين ولمان', 'بني عزيز', 'جميلة'],
      'عنابة': ['عنابة', 'الحجار', 'سيدي عمار', 'برحال', 'عين البردة'],
      'بجاية': ['بجاية', 'أقبو', 'تيشي', 'صدوق', 'أميزور'],
      'تلمسان': ['تلمسان', 'المنصورة', 'شتوان', 'مغنية', 'ندرومة'],
      'باتنة': ['باتنة', 'عين التوتة', 'تيمقاد', 'أريس', 'منعة'],
      'بسكرة': ['بسكرة', 'طولقة', 'سيدي عقبة', 'الزيبان', 'أورلال'],
      'تيزي وزو': ['تيزي وزو', 'أزازقة', 'تيغزيرت', 'عين الحمام', 'بني دوالة'],
      'البليدة': ['البليدة', 'الأفرون', 'بوفاريك', 'الشريعة', 'موزاية'],
      'مستغانم': ['مستغانم', 'سيدي علي', 'حجاج', 'عين تادلس', 'صور الغزلان'],
      'سكيكدة': ['سكيكدة', 'القل', 'عزابة', 'الحدائق', 'فلفلة'],
      'جيجل': ['جيجل', 'الطاهير', 'الميلية', 'العوانة', 'سيدي معروف'],
      'قالمة': ['قالمة', 'بوحمدان', 'الحجار', 'بوحشانة', 'عين حسنية'],
      'معسكر': ['معسكر', 'سيق', 'تيغنيف', 'بوحنيفية', 'عين فارس'],
      'ورقلة': ['ورقلة', 'حاسي مسعود', 'تقرت', 'المقارين', 'تماسين'],
      'أدرار': ['أدرار', 'رقان', 'تيميمون', 'أولف', 'زاوية كنتة'],
      'الشلف': ['الشلف', 'تنس', 'الكريمية', 'الأبيض مجاجة', 'وادي فودة'],
      'الأغواط': ['الأغواط', 'عفلو', 'قصر الحيران', 'عين ماضي', 'الحويطة'],
      'أم البواقي': ['أم البواقي', 'عين بيضاء', 'عين مليلة', 'سيق', 'الضلعة'],
      'بشار': ['بشار', 'بني عباس', 'كرزاز', 'تبلبالة', 'العبادلة'],
      'البويرة': [
        'البويرة',
        'سور الغزلان',
        'الأخضرية',
        'برج عمر إدريس',
        'عين بسام',
      ],
      'تمنراست': ['تمنراست', 'عين قزام', 'عين صالح', 'إدلس', 'تين زاوتين'],
      'تبسة': ['تبسة', 'الشريعة', 'بئر العاتر', 'الحويجبات', 'نقرين'],
      'تيارت': ['تيارت', 'فرندة', 'سوقر', 'مهدية', 'عين كرمس'],
      'الجلفة': ['الجلفة', 'عين وسارة', 'حاسي بحبح', 'مسعد', 'الإدريسية'],
      'سعيدة': ['سعيدة', 'يوب', 'عين الحديد', 'سيدي بوبكر', 'أولاد براهيم'],
      'سيدي بلعباس': [
        'سيدي بلعباس',
        'تلاغ',
        'بن بادس',
        'مصطفى بن براهيم',
        'تسالة',
      ],
      'المدية': [
        'المدية',
        'بني سليمان',
        'قصر البخاري',
        'عين بوسيف',
        'شلالة العذاورة',
      ],
      'المسيلة': ['المسيلة', 'بوسعادة', 'سيدي عامر', 'عين الريش', 'الهامل'],
    };

    return citiesMap[state] ?? [];
  }
}
