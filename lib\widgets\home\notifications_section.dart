import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';

/// قسم الإشعارات والتذكيرات - Notifications Section
/// 
/// يعرض آخر الإشعارات والتذكيرات المهمة بتصميم مدمج:
/// - تذكيرات المواعيد
/// - إشعارات النظام
/// - تحديثات العلاج
/// - رسائل الأطباء
class NotificationsSection extends StatefulWidget {
  const NotificationsSection({super.key});

  @override
  State<NotificationsSection> createState() => _NotificationsSectionState();
}

class _NotificationsSectionState extends State<NotificationsSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // بيانات وهمية - ستأتي من قاعدة البيانات لاحقاً
  final List<NotificationItem> _notifications = [
    NotificationItem(
      title: 'موعد العلاج الطبيعي',
      message: 'لديك موعد غداً الساعة 10:00 صباحاً في مركز الأمل',
      type: NotificationType.appointment,
      time: 'منذ ساعة',
      isRead: false,
    ),
    NotificationItem(
      title: 'تحديث خطة العلاج',
      message: 'تم تحديث خطة العلاج الخاصة بك من قبل د. أحمد محمد',
      type: NotificationType.treatment,
      time: 'منذ 3 ساعات',
      isRead: false,
    ),
    NotificationItem(
      title: 'تذكير التمارين',
      message: 'حان وقت تمارين اليوم - 15 دقيقة من التمارين المنزلية',
      type: NotificationType.reminder,
      time: 'منذ 5 ساعات',
      isRead: true,
    ),
    NotificationItem(
      title: 'رسالة من الطبيب',
      message: 'د. سارة أحمد أرسلت لك رسالة حول نتائج الفحص الأخير',
      type: NotificationType.message,
      time: 'أمس',
      isRead: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));

    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = _notifications.where((n) => !n.isRead).length;
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان مع عدد الإشعارات غير المقروءة
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 24,
                    decoration: BoxDecoration(
                      color: AppColors.warning,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: AppDimensions.marginMedium),
                  Text(
                    'الإشعارات والتذكيرات',
                    style: AppTextStyles.headline4.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (unreadCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.error,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$unreadCount',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textOnPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  const SizedBox(width: AppDimensions.marginSmall),
                  GestureDetector(
                    onTap: _handleViewAllTap,
                    child: Text(
                      'عرض الكل',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppDimensions.marginLarge),
            
            // قائمة الإشعارات
            Container(
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadow.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: _notifications.take(3).map((notification) {
                  final index = _notifications.indexOf(notification);
                  return _buildNotificationItem(notification, index);
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(NotificationItem notification, int index) {
    final isLast = index == 2 || index == _notifications.length - 1;
    
    return Container(
      decoration: BoxDecoration(
        border: isLast ? null : Border(
          bottom: BorderSide(
            color: AppColors.border.withValues(alpha: 0.5),
            width: 0.5,
          ),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          onTap: () => _handleNotificationTap(notification),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            child: Row(
              children: [
                // أيقونة النوع
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getNotificationColor(notification.type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                  ),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: _getNotificationColor(notification.type),
                    size: 20,
                  ),
                ),
                
                const SizedBox(width: AppDimensions.marginMedium),
                
                // المحتوى
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontWeight: notification.isRead 
                                    ? FontWeight.w500 
                                    : FontWeight.bold,
                                color: notification.isRead 
                                    ? AppColors.textPrimary 
                                    : AppColors.textPrimary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      
                      const SizedBox(height: 2),
                      
                      Text(
                        notification.message,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 4),
                      
                      Text(
                        notification.time,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary.withValues(alpha: 0.7),
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.appointment:
        return AppColors.primary;
      case NotificationType.treatment:
        return AppColors.success;
      case NotificationType.reminder:
        return AppColors.warning;
      case NotificationType.message:
        return AppColors.info;
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.appointment:
        return Icons.event_outlined;
      case NotificationType.treatment:
        return Icons.medical_services_outlined;
      case NotificationType.reminder:
        return Icons.alarm_outlined;
      case NotificationType.message:
        return Icons.message_outlined;
    }
  }

  void _handleNotificationTap(NotificationItem notification) {
    // TODO: Navigate to notification details or relevant screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح إشعار: ${notification.title}'),
        backgroundColor: _getNotificationColor(notification.type),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
      ),
    );
  }

  void _handleViewAllTap() {
    // TODO: Navigate to all notifications screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('سيتم فتح جميع الإشعارات قريباً...'),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
      ),
    );
  }
}

/// أنواع الإشعارات
enum NotificationType {
  appointment,
  treatment,
  reminder,
  message,
}

/// نموذج الإشعار
class NotificationItem {
  final String title;
  final String message;
  final NotificationType type;
  final String time;
  final bool isRead;

  NotificationItem({
    required this.title,
    required this.message,
    required this.type,
    required this.time,
    required this.isRead,
  });
}
