import 'package:flutter/material.dart';
import '../theme/app_styles.dart';
import '../theme/app_icons.dart';
import '../config/app_config.dart';

/// About App Screen - Information about Khotwa app
/// Displays app purpose, features, and contact information
/// Supports RTL layout and Arabic typography

class AboutAppScreen extends StatelessWidget {
  const AboutAppScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          title: const Text(
            'حول التطبيق',
            style: AppTextStyles.headline4,
          ),
          leading: Icon<PERSON>utton(
            icon: Icon(AppIcons.getBackIcon(TextDirection.rtl)),
            onPressed: () => Navigator.of(context).pop(),
          ),
          elevation: 0,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAppHeader(),
              const SizedBox(height: AppDimensions.marginXLarge),
              _buildAppDescription(),
              const SizedBox(height: AppDimensions.marginLarge),
              _buildFeatures(),
              const SizedBox(height: AppDimensions.marginLarge),
              _buildTargetAudience(),
              const SizedBox(height: AppDimensions.marginLarge),
              _buildAppInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppHeader() {
    return Center(
      child: Column(
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
            ),
            child: const Icon(
              Icons.accessibility_new,
              size: 50,
              color: AppColors.textOnPrimary,
            ),
          ),
          const SizedBox(height: AppDimensions.marginMedium),
          Text(
            AppConfig.appName,
            style: AppTextStyles.headline2.copyWith(
              color: AppColors.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.marginSmall),
          Text(
            'الإصدار ${AppConfig.appVersion}',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppDescription() {
    return _buildSection(
      title: 'عن التطبيق',
      icon: Icons.info_outline,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppConfig.appDescription,
            style: AppTextStyles.bodyLarge,
          ),
          const SizedBox(height: AppDimensions.marginMedium),
          Text(
            'يهدف التطبيق إلى تجاوز الحواجز الجغرافية والمعرفية عبر تقديم خدمات استشارية، تعليمية، وتواصلية تساعد المستخدم على اتخاذ خطوة نحو الاستقلالية والكرامة.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatures() {
    return _buildSection(
      title: 'أهداف التطبيق',
      icon: Icons.star_outline,
      content: Column(
        children: [
          _buildFeatureItem(
            icon: Icons.local_hospital_outlined,
            title: 'الوصول للمراكز المتخصصة',
            description: 'تسهيل الوصول إلى مراكز العلاج والتأهيل الحركي',
          ),
          _buildFeatureItem(
            icon: Icons.people_outline,
            title: 'ربط المرضى بالمختصين',
            description: 'ربط المرضى بالمختصين المناسبين حسب الموقع ونوع الخدمة',
          ),
          _buildFeatureItem(
            icon: Icons.school_outlined,
            title: 'محتوى تثقيفي',
            description: 'توفير محتوى تثقيفي ونفسي داعم للمستخدمين وذويهم',
          ),
          _buildFeatureItem(
            icon: Icons.accessibility_new,
            title: 'دعم الأطراف الصناعية',
            description: 'تقليل العوائق أمام المصابين المحتاجين لأطراف صناعية',
          ),
          _buildFeatureItem(
            icon: Icons.schedule,
            title: 'تسريع الحجز',
            description: 'تسريع عملية الحجز والتواصل مع الجهات المختصة',
          ),
        ],
      ),
    );
  }

  Widget _buildTargetAudience() {
    return _buildSection(
      title: 'الفئات المستفيدة',
      icon: Icons.group_outlined,
      content: Column(
        children: [
          _buildAudienceItem(
            icon: Icons.wheelchair_pickup,
            title: 'ذوو الإعاقة الحركية',
            description: 'الأفراد ذوو الطرف المبتور ومحدودو الحركة',
          ),
          _buildAudienceItem(
            icon: Icons.family_restroom,
            title: 'أولياء الأمور',
            description: 'الأقارب الباحثون عن رعاية مناسبة',
          ),
          _buildAudienceItem(
            icon: Icons.business,
            title: 'المراكز الطبية',
            description: 'المراكز المختصة في الأطراف الاصطناعية والعلاج الفيزيائي',
          ),
          _buildAudienceItem(
            icon: Icons.medical_information,
            title: 'الأطباء والمعالجون',
            description: 'المختصون المعتمدون في مجال التأهيل',
          ),
        ],
      ),
    );
  }

  Widget _buildAppInfo() {
    return _buildSection(
      title: 'معلومات التطبيق',
      icon: Icons.settings_outlined,
      content: Column(
        children: [
          _buildInfoRow('الإصدار', AppConfig.appVersion),
          _buildInfoRow('نوع التطبيق', 'تطبيق طبي مساعد'),
          _buildInfoRow('اللغة', 'العربية'),
          _buildInfoRow('المنصة', 'Android & iOS'),
          _buildInfoRow('آخر تحديث', 'يوليو 2025'),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget content,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.primary,
                size: AppDimensions.iconMedium,
              ),
              const SizedBox(width: AppDimensions.marginSmall),
              Text(
                title,
                style: AppTextStyles.headline4.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.marginMedium),
          content,
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.marginMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingSmall),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: AppDimensions.iconMedium,
            ),
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: AppFonts.semiBold,
                  ),
                ),
                const SizedBox(height: AppDimensions.marginXSmall),
                Text(
                  description,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAudienceItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return _buildFeatureItem(
      icon: icon,
      title: title,
      description: description,
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.marginSmall),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: AppFonts.medium,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
