import 'package:flutter/material.dart';
import 'package:line_icons/line_icons.dart';

/// App Icons - Centralized icon references
/// Following the guidelines for consistent icon usage
/// Supports RTL layout considerations

class AppIcons {
  // Authentication Icons
  static const IconData email = Icons.email_outlined;
  static const IconData emailFilled = Icons.email;
  static const IconData password = Icons.lock_outline;
  static const IconData passwordFilled = Icons.lock;
  static const IconData visibility = Icons.visibility_outlined;
  static const IconData visibilityOff = Icons.visibility_off_outlined;
  static const IconData person = Icons.person_outline;
  static const IconData personFilled = Icons.person;

  // Navigation Icons
  static const IconData home = Icons.home_outlined;
  static const IconData homeFilled = Icons.home;
  static const IconData back = Icons.arrow_back_ios;
  static const IconData forward = Icons.arrow_forward_ios;
  static const IconData close = Icons.close;
  static const IconData menu = Icons.menu;

  // Action Icons
  static const IconData add = Icons.add;
  static const IconData edit = Icons.edit_outlined;
  static const IconData delete = Icons.delete_outline;
  static const IconData save = Icons.save_outlined;
  static const IconData cancel = Icons.cancel_outlined;
  static const IconData check = Icons.check;
  static const IconData checkCircle = Icons.check_circle_outline;

  // Communication Icons
  static const IconData phone = Icons.phone_outlined;
  static const IconData phoneFilled = Icons.phone;
  static const IconData message = Icons.message_outlined;
  static const IconData messageFilled = Icons.message;
  static const IconData chat = Icons.chat_outlined;
  static const IconData chatFilled = Icons.chat;
  static const IconData video = Icons.videocam_outlined;
  static const IconData videoFilled = Icons.videocam;

  // Medical Icons
  static const IconData medical = Icons.medical_services_outlined;
  static const IconData medicalFilled = Icons.medical_services;
  static const IconData hospital = Icons.local_hospital_outlined;
  static const IconData hospitalFilled = Icons.local_hospital;
  static const IconData doctor = Icons.medical_information_outlined;
  static const IconData doctorFilled = Icons.medical_information;
  static const IconData health = Icons.health_and_safety_outlined;
  static const IconData healthFilled = Icons.health_and_safety;

  // Location Icons
  static const IconData location = Icons.location_on_outlined;
  static const IconData locationFilled = Icons.location_on;
  static const IconData map = Icons.map_outlined;
  static const IconData mapFilled = Icons.map;
  static const IconData directions = Icons.directions_outlined;
  static const IconData directionsFilled = Icons.directions;

  // Status Icons
  static const IconData success = Icons.check_circle;
  static const IconData error = Icons.error;
  static const IconData warning = Icons.warning;
  static const IconData info = Icons.info;
  static const IconData loading = Icons.hourglass_empty;

  // Settings Icons
  static const IconData settings = Icons.settings_outlined;
  static const IconData settingsFilled = Icons.settings;
  static const IconData profile = Icons.account_circle_outlined;
  static const IconData profileFilled = Icons.account_circle;
  static const IconData language = Icons.language;
  static const IconData theme = Icons.palette_outlined;

  // Search and Filter Icons
  static const IconData search = Icons.search;
  static const IconData filter = Icons.filter_list;
  static const IconData sort = Icons.sort;
  static const IconData clear = Icons.clear;

  // Media Icons
  static const IconData play = Icons.play_arrow;
  static const IconData pause = Icons.pause;
  static const IconData stop = Icons.stop;
  static const IconData volume = Icons.volume_up;
  static const IconData volumeOff = Icons.volume_off;
  static const IconData fullscreen = Icons.fullscreen;
  static const IconData fullscreenExit = Icons.fullscreen_exit;

  // File and Document Icons
  static const IconData file = Icons.description_outlined;
  static const IconData fileFilled = Icons.description;
  static const IconData download = Icons.download;
  static const IconData upload = Icons.upload;
  static const IconData share = Icons.share;
  static const IconData print = Icons.print;

  // Calendar and Time Icons
  static const IconData calendar = Icons.calendar_today;
  static const IconData calendarFilled = Icons.calendar_month;
  static const IconData time = Icons.access_time;
  static const IconData timeFilled = Icons.schedule;
  static const IconData date = Icons.date_range;

  // Accessibility Icons
  static const IconData accessibility = Icons.accessibility;
  static const IconData wheelchair = Icons.accessible;
  static const IconData hearing = Icons.hearing;
  static const IconData visuallyImpaired = Icons.visibility_off;

  // Social Icons (using material icons as fallback)
  static const IconData facebook = Icons.facebook;
  static const IconData twitter = Icons.alternate_email;
  static const IconData instagram = Icons.camera_alt;
  static const IconData linkedin = Icons.business;
  static const IconData whatsapp = Icons.chat;
  static const IconData telegram = Icons.send;

  // Line Icons (using line_icons package)
  static const IconData heartLine = LineIcons.heart;
  static const IconData starLine = LineIcons.star;
  static const IconData bookmarkLine = LineIcons.bookmark;
  static const IconData commentLine = LineIcons.comment;
  static const IconData likeLine = LineIcons.thumbsUp;
  static const IconData shareLine = LineIcons.share;

  // Custom App-specific Icons
  static const IconData prosthetic = Icons.accessibility_new;
  static const IconData therapy = Icons.self_improvement;
  static const IconData rehabilitation = Icons.fitness_center;
  static const IconData support = Icons.support_agent;
  static const IconData appointment = Icons.event_available;
  static const IconData consultation = Icons.video_call;

  // RTL-aware directional icons
  static IconData getBackIcon(TextDirection textDirection) {
    return textDirection == TextDirection.rtl
        ? Icons.arrow_forward_ios
        : Icons.arrow_back_ios;
  }

  static IconData getForwardIcon(TextDirection textDirection) {
    return textDirection == TextDirection.rtl
        ? Icons.arrow_back_ios
        : Icons.arrow_forward_ios;
  }

  static IconData getMenuIcon(TextDirection textDirection) {
    return textDirection == TextDirection.rtl ? Icons.menu_open : Icons.menu;
  }

  // Icon size helpers
  static Widget small(IconData icon, {Color? color}) {
    return Icon(icon, size: 16.0, color: color);
  }

  static Widget medium(IconData icon, {Color? color}) {
    return Icon(icon, size: 24.0, color: color);
  }

  static Widget large(IconData icon, {Color? color}) {
    return Icon(icon, size: 32.0, color: color);
  }

  static Widget extraLarge(IconData icon, {Color? color}) {
    return Icon(icon, size: 48.0, color: color);
  }

  // Colored icon helpers
  static Widget primary(IconData icon, {double? size}) {
    return Icon(icon, size: size ?? 24.0, color: const Color(0xFF2E7D32));
  }

  static Widget secondary(IconData icon, {double? size}) {
    return Icon(icon, size: size ?? 24.0, color: const Color(0xFF1976D2));
  }

  static Widget accent(IconData icon, {double? size}) {
    return Icon(icon, size: size ?? 24.0, color: const Color(0xFFFF9800));
  }

  static Widget errorIcon(IconData icon, {double? size}) {
    return Icon(icon, size: size ?? 24.0, color: const Color(0xFFF44336));
  }

  static Widget successIcon(IconData icon, {double? size}) {
    return Icon(icon, size: size ?? 24.0, color: const Color(0xFF4CAF50));
  }

  static Widget warningIcon(IconData icon, {double? size}) {
    return Icon(icon, size: size ?? 24.0, color: const Color(0xFFFF9800));
  }
}
