# 🧠 AI Agent Coding Guidelines – Application Development

This guide defines the best practices for building a clean, scalable, and maintainable app using an AI-powered coder (e.g., Augment Code or AgentCoder). Follow these standards to ensure a consistent and professional project structure.

---

## ✅ 1. Data Separation
- Store all static or structured content (texts, conversations, lists) in:
  ```
  assets/data/
  ```
- Use modular and clearly named files:
  - `home_content.json`
  - `faq_data.json`
  - `arabic_dialogues.json`

---

## 🎨 2. Unified Styling Guide
Create a centralized style file to ensure UI consistency:

```
lib/theme/app_styles.dart
```

This file should contain:
- `AppColors` – All color definitions
- `AppFonts` – Fonts and families
- `AppTextStyles` – Title, body, caption...
- `AppDimensions` – Sizes, paddings, spacings

> 💡 Include comments and documentation for each item.


## 🧩 4. Task Breakdown & File Structure

Organize your work into:

- **Main Tasks**:
  - UI Screens
  - Logic & Controllers
  - Database
  - Settings & Configs
- **Subtasks** per screen/feature.

Project structure example:
```
lib/
├── models/
├── screens/
├── widgets/
├── data/
├── theme/
├── config/
├── providers/
```

> 🧠 Use `snake_case` for files and `CamelCase` for classes.

---

## ⚙ 5. Clean Code & Config File

Centralize all changeable variables in:

```
lib/config/app_config.dart
```

This includes:
- App name & version
- Default language
- Feature toggles
- API keys (mock)

Use constants for easy editing and code clarity.

---

## ✍️ 6. Fonts & Icons

- Use beautiful and clear fonts such as:
  - **Tajawal**
  - **Cairo**
- Store in:
  ```
  assets/fonts/
  ```

- Use clean icon libraries:
  - `flutter_vector_icons`
  - `lucide_icons`
  - `line_icons`

Reference all icons from one file:
```
lib/theme/app_icons.dart
```

---

## 🖼 7. Image Placeholders & Documentation

Add a general placeholder image:
```
assets/images/placeholder.png
```

Create a README for image usage:
```
assets/images/README.md
```

Include:
- Required sizes (e.g., 200x300 px)
- File types (e.g., PNG, JPG, SVG)
- Naming conventions

---

## 📱 8. Consistent Layout & UX

Maintain a shared layout across all screens using:
```
lib/widgets/base_screen.dart
```

- Use `Scaffold`, `AppBar`, unified spacing
- Support RTL layouts if Arabic is used
- Responsive design with `MediaQuery` or `LayoutBuilder`

