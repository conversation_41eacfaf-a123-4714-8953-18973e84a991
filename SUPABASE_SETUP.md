# إعداد Supabase لتطبيق خطوة

تم إنشاء مشروع Supabase جديد لتطبيق خطوة. اتبع هذه الخطوات لإكمال الإعداد.

## 📋 معلومات المشروع

- **اسم المشروع**: khotwa-app
- **معرف المشروع**: ibhyrzexaiqwiiyutblg
- **المنطقة**: eu-west-3
- **الرابط**: https://ibhyrzexaiqwiiyutblg.supabase.co

## 🔑 الخطوة 1: الحصول على مفاتيح API

1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. اختر مشروع `khotwa-app`
3. اذهب إلى Settings > API
4. انسخ المفاتيح التالية:
   - `Project URL`
   - `anon public key`

## 🔧 الخطوة 2: تحديث إعدادات التطبيق

قم بتحديث الملف `lib/config/app_config.dart`:

```dart
// Supabase Configuration
static const String supabaseUrl = 'https://ibhyrzexaiqwiiyutblg.supabase.co';
static const String supabaseAnonKey = 'YOUR_ACTUAL_ANON_KEY_HERE';
```

## 🗄️ الخطوة 3: إعداد قاعدة البيانات

1. اذهب إلى Supabase Dashboard > SQL Editor
2. انسخ محتوى الملف `supabase_setup.sql`
3. الصق الكود في SQL Editor واضغط Run

هذا سيقوم بإنشاء:
- جدول `profiles` للملفات الشخصية
- جدول `medical_centers` للمراكز الطبية
- جدول `specialists` للمختصين
- جدول `appointments` للمواعيد
- السياسات الأمنية (RLS Policies)
- البيانات التجريبية

## 🔐 الخطوة 4: إعداد المصادقة

1. اذهب إلى Authentication > Settings
2. تأكد من تفعيل:
   - Enable email confirmations
   - Enable secure email change
3. في Email Templates، تأكد من أن الرسائل باللغة العربية (اختياري)

## 📧 الخطوة 5: إعداد البريد الإلكتروني

1. اذهب إلى Settings > Auth
2. في SMTP Settings، قم بإعداد خدمة البريد الإلكتروني
3. أو استخدم الخدمة المدمجة لـ Supabase

## 🧪 الخطوة 6: اختبار التكامل

1. شغل التطبيق:
```bash
flutter run
```

2. جرب العمليات التالية:
   - تسجيل حساب جديد
   - تسجيل الدخول
   - تحديث الملف الشخصي
   - عرض المراكز الطبية

## 🔍 استكشاف الأخطاء

### خطأ في الاتصال
- تأكد من صحة `supabaseUrl` و `supabaseAnonKey`
- تأكد من اتصال الإنترنت

### خطأ في المصادقة
- تأكد من تفعيل Email confirmations
- تحقق من إعدادات SMTP

### خطأ في قاعدة البيانات
- تأكد من تشغيل `supabase_setup.sql` بنجاح
- تحقق من السياسات الأمنية (RLS)

## 📊 هيكل قاعدة البيانات

### جدول profiles
```sql
- id (UUID) - مرجع لـ auth.users
- full_name (TEXT) - الاسم الكامل
- user_type (TEXT) - نوع المستخدم
- phone_number (TEXT) - رقم الهاتف
- date_of_birth (DATE) - تاريخ الميلاد
- gender (TEXT) - الجنس
- city (TEXT) - المدينة
- address (TEXT) - العنوان
- emergency_contact (TEXT) - جهة الاتصال الطارئة
- medical_conditions (TEXT[]) - الحالات الطبية
- medications (TEXT[]) - الأدوية
- notes (TEXT) - ملاحظات
```

### جدول medical_centers
```sql
- id (UUID) - المعرف الفريد
- name (TEXT) - اسم المركز
- description (TEXT) - الوصف
- address (TEXT) - العنوان
- city (TEXT) - المدينة
- phone_number (TEXT) - رقم الهاتف
- services (TEXT[]) - الخدمات
- specialties (TEXT[]) - التخصصات
- rating (DECIMAL) - التقييم
```

### جدول appointments
```sql
- id (UUID) - المعرف الفريد
- patient_id (UUID) - معرف المريض
- specialist_id (UUID) - معرف المختص
- appointment_date (DATE) - تاريخ الموعد
- appointment_time (TIME) - وقت الموعد
- status (TEXT) - حالة الموعد
- notes (TEXT) - ملاحظات
```

## 🚀 الميزات المتاحة

بعد إكمال الإعداد، ستكون الميزات التالية متاحة:

- ✅ تسجيل المستخدمين الجدد
- ✅ تسجيل الدخول والخروج
- ✅ إدارة الملفات الشخصية
- ✅ عرض المراكز الطبية
- ✅ البحث عن المختصين
- ✅ حجز المواعيد
- ✅ إدارة المواعيد

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من وحدة التحكم في المتصفح للأخطاء
2. راجع سجلات Supabase في Dashboard
3. تأكد من صحة إعدادات RLS

## 🔄 التحديثات المستقبلية

لإضافة ميزات جديدة:
1. قم بتحديث schema قاعدة البيانات
2. أضف الخدمات المطلوبة في `lib/data/`
3. حدث الـ providers في `lib/providers/`
4. أنشئ الشاشات الجديدة في `lib/screens/`

---

**ملاحظة**: تأكد من الاحتفاظ بنسخة احتياطية من قاعدة البيانات قبل إجراء أي تغييرات كبيرة.
