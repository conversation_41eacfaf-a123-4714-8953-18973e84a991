# 🩺 واجهة مراحل تسجيل الملف الطبي - Khotwa

تم إنشاء واجهة متعددة المراحل (Multi-step Form Wizard) لجمع معلومات طبية شاملة عن المستخدم (المريض) بعد تسجيل الدخول لأول مرة.

## 🎯 الهدف

إنشاء ملف طبي شامل للمريض يساعد في:
- تقديم استشارات طبية دقيقة ومخصصة
- العثور على أقرب المراكز الطبية والمختصين
- تتبع التاريخ الطبي والتطورات
- تحسين تجربة المستخدم وجودة الخدمة

## 📋 المراحل الستة

### 1️⃣ المرحلة الأولى: المعلومات الشخصية
**الملف**: `lib/widgets/medical_profile_steps/step1_personal_info.dart`

**المحتوى**:
- الاسم الكامل (مطلوب)
- الجنس (ذكر/أنثى) (مطلوب)
- تاريخ الميلاد (مطلوب)
- رقم الهاتف (مطلوب)

**التحقق**: جميع الحقول مطلوبة ويتم التحقق من صحة البيانات

### 2️⃣ المرحلة الثانية: الوضع الصحي
**الملف**: `lib/widgets/medical_profile_steps/step2_medical_condition.dart`

**المحتوى**:
- نوع الإعاقة الحركية (مطلوب) - قائمة منسدلة
- تاريخ الإصابة (اختياري)
- تفاصيل الحالة الطبية (اختياري)
- الوضع الحالي والصعوبات (اختياري)

**أنواع الإعاقة المتاحة**:
- بتر في الساق
- بتر في الذراع
- بتر في اليد
- بتر في القدم
- شلل في الأطراف السفلية
- شلل في الأطراف العلوية
- ضعف في العضلات
- تشوه خلقي
- إصابة في العمود الفقري
- أخرى

### 3️⃣ المرحلة الثالثة: الطرف الاصطناعي
**الملف**: `lib/widgets/medical_profile_steps/step3_prosthetic_info.dart`

**المحتوى**:
- هل يستخدم طرفاً اصطناعياً؟ (نعم/لا)
- نوع الطرف الاصطناعي (إذا كان يستخدم)
- تاريخ التركيب (اختياري)
- الشركة المصنعة (اختياري)
- الصعوبات الحالية (اختياري)

**أنواع الأطراف الاصطناعية**:
- طرف اصطناعي للساق
- طرف اصطناعي للذراع
- طرف اصطناعي لليد
- طرف اصطناعي للقدم
- جهاز مساعد للمشي
- كرسي متحرك
- عكازات
- أخرى

### 4️⃣ المرحلة الرابعة: المرفقات الطبية (اختياري)
**الملف**: `lib/widgets/medical_profile_steps/step4_medical_attachments.dart`

**المحتوى**:
- رفع التقارير الطبية (PDF)
- رفع الصور الطبية (JPG, PNG)
- إمكانية تخطي هذه المرحلة

**ملاحظة**: هذه المرحلة اختيارية ويمكن إضافة الملفات لاحقاً من الملف الشخصي

### 5️⃣ المرحلة الخامسة: العنوان والموقع
**الملف**: `lib/widgets/medical_profile_steps/step5_location_info.dart`

**المحتوى**:
- المنطقة (مطلوب) - قائمة بجميع مناطق السعودية
- المدينة (مطلوب) - تتغير حسب المنطقة المختارة
- العنوان التفصيلي (اختياري)
- مشاركة الموقع الجغرافي (اختياري)

**المناطق المتاحة**: جميع مناطق المملكة العربية السعودية مع مدنها

### 6️⃣ المرحلة السادسة: الموافقة النهائية
**الملف**: `lib/widgets/medical_profile_steps/step6_final_consent.dart`

**المحتوى**:
- ملخص الملف الطبي
- نسبة الإكمال
- الموافقة على شروط الخصوصية (مطلوب)
- زر "إكمال الملف الطبي"

## 🏗️ البنية التقنية

### النماذج (Models)
- **`MedicalProfile`**: نموذج شامل لجميع بيانات الملف الطبي
- **الملف**: `lib/models/medical_profile_model.dart`

### مزودي الحالة (Providers)
- **`MedicalProfileProvider`**: إدارة حالة البيانات أثناء المعالج
- **الملف**: `lib/providers/medical_profile_provider.dart`

### الخدمات (Services)
- **`MedicalProfileService`**: التعامل مع قاعدة البيانات
- **الملف**: `lib/data/medical_profile_service.dart`

### الشاشة الرئيسية
- **`MedicalProfileWizardScreen`**: الشاشة الرئيسية للمعالج
- **الملف**: `lib/screens/medical_profile_wizard_screen.dart`

## 🗄️ قاعدة البيانات

تم تحديث جدول `profiles` في Supabase ليشمل:

```sql
-- Medical condition information
disability_type TEXT,
injury_date DATE,
medical_details TEXT,
current_condition TEXT,

-- Prosthetic information
uses_prosthetic BOOLEAN DEFAULT FALSE,
prosthetic_type TEXT,
prosthetic_install_date DATE,
current_difficulties TEXT,
prosthetic_brand TEXT,

-- Medical attachments
medical_reports TEXT[],
medical_images TEXT[],

-- Location information
state TEXT,
latitude DECIMAL,
longitude DECIMAL,
share_location BOOLEAN DEFAULT FALSE,

-- Profile completion
privacy_consent BOOLEAN DEFAULT FALSE,
is_profile_complete BOOLEAN DEFAULT FALSE,
completed_at TIMESTAMP WITH TIME ZONE,
```

## 🔄 تدفق العمل

1. **بعد تسجيل الدخول**: يتم التحقق من `is_profile_complete`
2. **إذا لم يكن مكتملاً**: توجيه المستخدم إلى `MedicalProfileWizardScreen`
3. **التنقل بين المراحل**: استخدام `PageController` مع تحقق من صحة البيانات
4. **الحفظ التلقائي**: حفظ التقدم في كل مرحلة
5. **الإكمال**: تحديث `is_profile_complete = true` وتوجيه إلى الصفحة الرئيسية

## ✨ الميزات

- ✅ **واجهة RTL** كاملة باللغة العربية
- ✅ **تحقق من صحة البيانات** في كل مرحلة
- ✅ **حفظ تلقائي** للتقدم
- ✅ **تصميم متجاوب** يعمل على جميع الأحجام
- ✅ **رسائل خطأ واضحة** ومفيدة
- ✅ **إمكانية التخطي** للمراحل الاختيارية
- ✅ **ملخص شامل** قبل الإكمال
- ✅ **شريط تقدم** يوضح المرحلة الحالية

## 🚀 الاستخدام

بعد إكمال الملف الطبي، سيتمكن المستخدم من:

1. **الوصول للصفحة الرئيسية** مع جميع الميزات
2. **البحث عن المراكز الطبية** القريبة
3. **حجز المواعيد** مع المختصين
4. **تلقي توصيات مخصصة** حسب حالته
5. **تتبع التاريخ الطبي** والمواعيد

## 🔧 التخصيص

يمكن بسهولة:
- إضافة مراحل جديدة
- تعديل الحقول المطلوبة
- إضافة أنواع إعاقة جديدة
- تخصيص التصميم والألوان
- إضافة المزيد من عمليات التحقق

## 📱 التجربة

الواجهة مصممة لتكون:
- **سهلة الاستخدام** للمرضى من جميع الأعمار
- **واضحة ومفهومة** بلغة بسيطة
- **سريعة** في التحميل والاستجابة
- **آمنة** مع حماية البيانات الطبية
- **شاملة** لجميع أنواع الإعاقات الحركية

---

**ملاحظة**: تأكد من تشغيل سكريبت `supabase_setup.sql` لإنشاء الجداول المطلوبة في قاعدة البيانات.
