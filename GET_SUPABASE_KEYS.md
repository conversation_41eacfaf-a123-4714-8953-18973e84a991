# 🔑 كيفية الحصول على مفاتيح Supabase الصحيحة

المشكلة الحالية: `Err: invalid api key` تحدث لأن المفتاح المستخدم في التطبيق غير صحيح.

## 🚀 الحل السريع

### الخطوة 1: اذهب إلى Supabase Dashboard
1. افتح [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. سجل دخولك إلى حسابك
3. ابحث عن مشروع `khotwa-app` أو أنشئ مشروع جديد

### الخطوة 2: احصل على المفاتيح
1. اختر المشروع
2. اذهب إلى **Settings** (الإعدادات)
3. اختر **API** من القائمة الجانبية
4. انسخ المعلومات التالية:
   - **Project URL** (رابط المشروع)
   - **anon public** key (المفتاح العام)

### الخطوة 3: حدث التطبيق
افتح الملف `lib/config/app_config.dart` وحدث هذه الأسطر:

```dart
// Supabase Configuration
static const String supabaseUrl = 'YOUR_PROJECT_URL_HERE';
static const String supabaseAnonKey = 'YOUR_ANON_KEY_HERE';
```

## 🆕 إنشاء مشروع جديد (إذا لم يكن موجود)

إذا لم تجد مشروع `khotwa-app`:

1. اضغط **New Project**
2. اختر Organization
3. اسم المشروع: `khotwa-app`
4. كلمة مرور قاعدة البيانات: `KhotwaApp2025!@#`
5. المنطقة: اختر الأقرب لك
6. اضغط **Create new project**

انتظر 2-3 دقائق حتى يكتمل الإعداد.

## 🗄️ إعداد قاعدة البيانات

بعد الحصول على المفاتيح:

1. اذهب إلى **SQL Editor** في Supabase
2. انسخ محتوى الملف `supabase_setup.sql`
3. الصق الكود واضغط **Run**

## ✅ اختبار التطبيق

بعد تحديث المفاتيح:

```bash
flutter run
```

جرب تسجيل حساب جديد - يجب أن يعمل الآن!

## 🔍 استكشاف الأخطاء

### إذا ظهر خطأ "Project not found"
- تأكد من صحة `supabaseUrl`
- تأكد أن المشروع نشط في Dashboard

### إذا ظهر خطأ "Invalid API key"
- تأكد من نسخ `anon public` key وليس `service_role`
- تأكد من عدم وجود مسافات إضافية

### إذا ظهر خطأ "Network error"
- تحقق من اتصال الإنترنت
- تأكد من عدم حجب Supabase بواسطة Firewall

## 📞 المساعدة

إذا واجهت مشاكل:
1. تحقق من Console في المتصفح للأخطاء التفصيلية
2. تأكد من تشغيل `flutter clean && flutter pub get`
3. أعد تشغيل التطبيق

---

**مهم**: احتفظ بمفاتيح API آمنة ولا تشاركها علناً!
