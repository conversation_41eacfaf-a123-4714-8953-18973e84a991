import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';

/// Profile Service for managing user profiles
/// Handles CRUD operations for user profile data
class ProfileService {
  static final ProfileService _instance = ProfileService._internal();
  factory ProfileService() => _instance;
  ProfileService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get current user's profile
  Future<UserProfile?> getCurrentUserProfile() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return null;

      final response = await _supabase
          .from('profiles')
          .select()
          .eq('id', user.id)
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  /// Update current user's profile
  Future<bool> updateProfile(UserProfile profile) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      await _supabase
          .from('profiles')
          .update(profile.toJson())
          .eq('id', user.id);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Create profile for new user
  Future<bool> createProfile({
    required String userId,
    required String fullName,
    String userType = 'patient',
  }) async {
    try {
      await _supabase.from('profiles').insert({
        'id': userId,
        'full_name': fullName,
        'user_type': userType,
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Update profile field
  Future<bool> updateProfileField(String field, dynamic value) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      await _supabase
          .from('profiles')
          .update({field: value})
          .eq('id', user.id);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get medical centers
  Future<List<Map<String, dynamic>>> getMedicalCenters({
    String? city,
    List<String>? services,
  }) async {
    try {
      var query = _supabase
          .from('medical_centers')
          .select()
          .eq('is_active', true);

      if (city != null && city.isNotEmpty) {
        query = query.eq('city', city);
      }

      if (services != null && services.isNotEmpty) {
        query = query.overlaps('services', services);
      }

      final response = await query.order('rating', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      return [];
    }
  }

  /// Get specialists
  Future<List<Map<String, dynamic>>> getSpecialists({
    String? specialization,
    String? medicalCenterId,
  }) async {
    try {
      var query = _supabase
          .from('specialists')
          .select('''
            *,
            medical_centers:medical_center_id(name, city, address)
          ''')
          .eq('is_available', true);

      if (specialization != null && specialization.isNotEmpty) {
        query = query.eq('specialization', specialization);
      }

      if (medicalCenterId != null && medicalCenterId.isNotEmpty) {
        query = query.eq('medical_center_id', medicalCenterId);
      }

      final response = await query.order('rating', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      return [];
    }
  }

  /// Create appointment
  Future<bool> createAppointment({
    required String specialistId,
    required String medicalCenterId,
    required DateTime appointmentDate,
    required String appointmentTime,
    String? notes,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      await _supabase.from('appointments').insert({
        'patient_id': user.id,
        'specialist_id': specialistId,
        'medical_center_id': medicalCenterId,
        'appointment_date': appointmentDate.toIso8601String().split('T')[0],
        'appointment_time': appointmentTime,
        'notes': notes,
        'status': 'scheduled',
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get user appointments
  Future<List<Map<String, dynamic>>> getUserAppointments() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      final response = await _supabase
          .from('appointments')
          .select('''
            *,
            specialists:specialist_id(
              specialization,
              medical_centers:medical_center_id(name, address, city)
            )
          ''')
          .eq('patient_id', user.id)
          .order('appointment_date', ascending: true);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      return [];
    }
  }

  /// Update appointment status
  Future<bool> updateAppointmentStatus(String appointmentId, String status) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return false;

      await _supabase
          .from('appointments')
          .update({'status': status})
          .eq('id', appointmentId)
          .eq('patient_id', user.id);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Cancel appointment
  Future<bool> cancelAppointment(String appointmentId) async {
    return await updateAppointmentStatus(appointmentId, 'cancelled');
  }

  /// Get available cities
  Future<List<String>> getAvailableCities() async {
    try {
      final response = await _supabase
          .from('medical_centers')
          .select('city')
          .eq('is_active', true);

      final cities = response
          .map((item) => item['city'] as String)
          .toSet()
          .toList();

      cities.sort();
      return cities;
    } catch (e) {
      return ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة'];
    }
  }

  /// Get available services
  Future<List<String>> getAvailableServices() async {
    try {
      final response = await _supabase
          .from('medical_centers')
          .select('services')
          .eq('is_active', true);

      final allServices = <String>{};
      for (final item in response) {
        final services = List<String>.from(item['services'] ?? []);
        allServices.addAll(services);
      }

      final servicesList = allServices.toList();
      servicesList.sort();
      return servicesList;
    } catch (e) {
      return [
        'الأطراف الاصطناعية',
        'العلاج الفيزيائي',
        'التأهيل الحركي',
        'العلاج الوظيفي',
        'جراحة العظام'
      ];
    }
  }

  /// Search medical centers and specialists
  Future<Map<String, List<Map<String, dynamic>>>> search(String query) async {
    try {
      // Search medical centers
      final centersResponse = await _supabase
          .from('medical_centers')
          .select()
          .or('name.ilike.%$query%,description.ilike.%$query%,city.ilike.%$query%')
          .eq('is_active', true)
          .limit(10);

      // Search specialists
      final specialistsResponse = await _supabase
          .from('specialists')
          .select('''
            *,
            medical_centers:medical_center_id(name, city)
          ''')
          .or('specialization.ilike.%$query%,bio.ilike.%$query%')
          .eq('is_available', true)
          .limit(10);

      return {
        'centers': List<Map<String, dynamic>>.from(centersResponse),
        'specialists': List<Map<String, dynamic>>.from(specialistsResponse),
      };
    } catch (e) {
      return {'centers': [], 'specialists': []};
    }
  }
}
