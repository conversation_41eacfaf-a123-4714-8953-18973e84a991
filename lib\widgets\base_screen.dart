import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_styles.dart';
import '../theme/app_icons.dart';

/// Base Screen Widget - Consistent layout foundation
/// Provides unified structure for all app screens
/// Supports RTL layout and responsive design

class BaseScreen extends StatelessWidget {
  final String? title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final Widget? endDrawer;
  final bool showAppBar;
  final bool showBackButton;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final VoidCallback? onBackPressed;
  final PreferredSizeWidget? bottom;
  final bool centerTitle;
  final Widget? leading;
  final double? elevation;

  const BaseScreen({
    super.key,
    this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.drawer,
    this.endDrawer,
    this.showAppBar = true,
    this.showBackButton = true,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.onBackPressed,
    this.bottom,
    this.centerTitle = true,
    this.leading,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: backgroundColor ?? AppColors.background,
        appBar: showAppBar ? _buildAppBar(context) : null,
        body: _buildResponsiveBody(context),
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: bottomNavigationBar,
        drawer: drawer,
        endDrawer: endDrawer,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: title != null
          ? Text(
              title!,
              style: AppTextStyles.headline4.copyWith(
                color: AppColors.textOnPrimary,
              ),
            )
          : null,
      centerTitle: centerTitle,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      elevation: elevation ?? 0,
      leading: _buildLeading(context),
      actions: actions,
      bottom: bottom,
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) return leading;

    if (showBackButton && Navigator.of(context).canPop()) {
      return IconButton(
        icon: Icon(AppIcons.getBackIcon(TextDirection.rtl)),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
        tooltip: 'رجوع',
      );
    }

    return null;
  }

  Widget _buildResponsiveBody(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine screen size category
        final screenWidth = constraints.maxWidth;
        final isTablet = screenWidth >= AppDimensions.tabletBreakpoint;
        final isDesktop = screenWidth >= AppDimensions.desktopBreakpoint;

        if (isDesktop) {
          return _buildDesktopLayout(context, constraints);
        } else if (isTablet) {
          return _buildTabletLayout(context, constraints);
        } else {
          return _buildMobileLayout(context, constraints);
        }
      },
    );
  }

  Widget _buildMobileLayout(BuildContext context, BoxConstraints constraints) {
    return SafeArea(child: body);
  }

  Widget _buildTabletLayout(BuildContext context, BoxConstraints constraints) {
    return SafeArea(
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxWidth: AppDimensions.tabletBreakpoint,
          ),
          child: body,
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context, BoxConstraints constraints) {
    return SafeArea(
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxWidth: AppDimensions.desktopBreakpoint * 0.8,
          ),
          child: body,
        ),
      ),
    );
  }
}

/// Responsive Padding Widget
/// Provides consistent padding that adapts to screen size
class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final EdgeInsets? mobile;
  final EdgeInsets? tablet;
  final EdgeInsets? desktop;

  const ResponsivePadding({
    super.key,
    required this.child,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;

        EdgeInsets padding;
        if (screenWidth >= AppDimensions.desktopBreakpoint) {
          padding =
              desktop ?? const EdgeInsets.all(AppDimensions.paddingXLarge);
        } else if (screenWidth >= AppDimensions.tabletBreakpoint) {
          padding = tablet ?? const EdgeInsets.all(AppDimensions.paddingLarge);
        } else {
          padding = mobile ?? const EdgeInsets.all(AppDimensions.paddingMedium);
        }

        return Padding(padding: padding, child: child);
      },
    );
  }
}

/// Responsive Column Widget
/// Provides responsive column layout with proper spacing
class ResponsiveColumn extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const ResponsiveColumn({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;

        // Adjust spacing based on screen size
        double spacing;
        if (screenWidth >= AppDimensions.desktopBreakpoint) {
          spacing = AppDimensions.marginXLarge;
        } else if (screenWidth >= AppDimensions.tabletBreakpoint) {
          spacing = AppDimensions.marginLarge;
        } else {
          spacing = AppDimensions.marginMedium;
        }

        return Column(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: crossAxisAlignment,
          mainAxisSize: mainAxisSize,
          children: _addSpacing(children, spacing),
        );
      },
    );
  }

  List<Widget> _addSpacing(List<Widget> children, double spacing) {
    if (children.isEmpty) return children;

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(height: spacing));
      }
    }
    return spacedChildren;
  }
}

/// Responsive Card Widget
/// Provides consistent card styling with responsive dimensions
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final double? elevation;
  final BorderRadius? borderRadius;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;

        // Adjust padding and elevation based on screen size
        EdgeInsets cardPadding;
        double cardElevation;

        if (screenWidth >= AppDimensions.desktopBreakpoint) {
          cardPadding =
              padding ?? const EdgeInsets.all(AppDimensions.paddingXLarge);
          cardElevation = elevation ?? AppDimensions.elevationMedium;
        } else if (screenWidth >= AppDimensions.tabletBreakpoint) {
          cardPadding =
              padding ?? const EdgeInsets.all(AppDimensions.paddingLarge);
          cardElevation = elevation ?? AppDimensions.elevationMedium;
        } else {
          cardPadding =
              padding ?? const EdgeInsets.all(AppDimensions.paddingMedium);
          cardElevation = elevation ?? AppDimensions.elevationLow;
        }

        return Container(
          margin: margin,
          decoration: BoxDecoration(
            color: color ?? AppColors.surface,
            borderRadius:
                borderRadius ??
                BorderRadius.circular(AppDimensions.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow,
                blurRadius: cardElevation * 2,
                offset: Offset(0, cardElevation / 2),
              ),
            ],
          ),
          child: Padding(padding: cardPadding, child: child),
        );
      },
    );
  }
}

/// System UI Overlay Style for consistent status bar

class AppSystemUIOverlayStyle {
  static const SystemUiOverlayStyle light = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.light,
    statusBarBrightness: Brightness.dark,
    systemNavigationBarColor: AppColors.surface,
    systemNavigationBarIconBrightness: Brightness.dark,
  );

  static const SystemUiOverlayStyle dark = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.light,
    systemNavigationBarColor: AppColors.surface,
    systemNavigationBarIconBrightness: Brightness.dark,
  );
}
