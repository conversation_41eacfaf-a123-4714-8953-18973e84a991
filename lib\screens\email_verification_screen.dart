import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_styles.dart';
import '../theme/app_icons.dart';
import '../config/app_config.dart';
import '../providers/auth_provider.dart';
import 'home_screen.dart';
import 'sign_in_screen.dart';

/// Email Verification Screen - Post-registration verification
/// Guides users through email verification process
/// Supports RTL layout and resend functionality

class EmailVerificationScreen extends StatefulWidget {
  final String email;

  const EmailVerificationScreen({
    super.key,
    required this.email,
  });

  @override
  State<EmailVerificationScreen> createState() => _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends State<EmailVerificationScreen> {
  bool _isResending = false;

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingLarge),
            child: Column(
              children: [
                const Spacer(),
                // _buildVerificationIcon(),
                const SizedBox(height: AppDimensions.marginXLarge),
                _buildTitle(),
                const SizedBox(height: AppDimensions.marginMedium),
                _buildDescription(),
                const SizedBox(height: AppDimensions.marginXLarge),
                _buildInstructions(),
                const SizedBox(height: AppDimensions.marginXLarge),
                _buildActionButtons(),
                const Spacer(),
                _buildBottomActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVerificationIcon() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusCircular),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: const Icon(
        Icons.mark_email_unread,
        size: 60,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      AppConfig.titleEmailVerification,
      style: AppTextStyles.headline2.copyWith(
        color: AppColors.primary,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription() {
    return Column(
      children: [
        Text(
          'تم إرسال رابط التحقق إلى:',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
            vertical: AppDimensions.paddingSmall,
          ),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
          ),
          child: Text(
            widget.email,
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.primary,
              fontWeight: AppFonts.semiBold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildInstructions() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                AppIcons.info,
                color: AppColors.primary,
                size: AppDimensions.iconMedium,
              ),
              const SizedBox(width: AppDimensions.marginSmall),
              Text(
                'خطوات التحقق',
                style: AppTextStyles.headline4.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.marginMedium),
          _buildInstructionStep(
            number: '1',
            text: 'تحقق من صندوق الوارد في بريدك الإلكتروني',
          ),
          _buildInstructionStep(
            number: '2',
            text: 'ابحث عن رسالة من تطبيق خطوة',
          ),
          _buildInstructionStep(
            number: '3',
            text: 'اضغط على رابط التحقق في الرسالة',
          ),
          _buildInstructionStep(
            number: '4',
            text: 'ارجع إلى التطبيق وسجل دخولك',
          ),
          const SizedBox(height: AppDimensions.marginMedium),
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingSmall),
            decoration: BoxDecoration(
              color: AppColors.warning.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            ),
            child: Row(
              children: [
                Icon(
                  AppIcons.warning,
                  color: AppColors.warning,
                  size: AppDimensions.iconSmall,
                ),
                const SizedBox(width: AppDimensions.marginSmall),
                Expanded(
                  child: Text(
                    'تحقق من مجلد الرسائل غير المرغوب فيها إذا لم تجد الرسالة',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.warning,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionStep({
    required String number,
    required String text,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.marginSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(AppDimensions.radiusCircular),
            ),
            child: Center(
              child: Text(
                number,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: AppFonts.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: ElevatedButton(
            onPressed: _isResending ? null : _handleResendVerification,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.accent,
              foregroundColor: AppColors.textOnPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
            ),
            child: _isResending
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.textOnPrimary),
                    ),
                  )
                : Text(
                    'إعادة إرسال رابط التحقق',
                    style: AppTextStyles.buttonMedium,
                  ),
          ),
        ),
        const SizedBox(height: AppDimensions.marginMedium),
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: OutlinedButton(
            onPressed: _checkVerificationStatus,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
            ),
            child: Text(
              'تحقق من حالة التحقق',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomActions() {
    return Column(
      children: [
        TextButton(
          onPressed: _navigateToSignIn,
          child: Text(
            'تم التحقق بالفعل؟ سجل دخولك',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primary,
              fontWeight: AppFonts.medium,
            ),
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        TextButton(
          onPressed: _handleSignOut,
          child: Text(
            'تسجيل الخروج',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  void _handleResendVerification() async {
    setState(() {
      _isResending = true;
    });

    final authProvider = context.read<AuthProvider>();
    final success = await authProvider.resendEmailVerification();

    setState(() {
      _isResending = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success 
                ? 'تم إعادة إرسال رابط التحقق'
                : 'فشل في إعادة الإرسال، حاول مرة أخرى',
          ),
          backgroundColor: success ? AppColors.success : AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
          ),
        ),
      );
    }
  }

  void _checkVerificationStatus() async {
    final authProvider = context.read<AuthProvider>();
    
    // Check if user is verified
    if (authProvider.isEmailVerified) {
      // Navigate to home screen
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => const HomeScreen(),
          ),
          (route) => false,
        );
      }
    } else {
      // Show message that verification is still pending
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('لم يتم التحقق بعد، يرجى المحاولة مرة أخرى'),
            backgroundColor: AppColors.warning,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            ),
          ),
        );
      }
    }
  }

  void _navigateToSignIn() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const SignInScreen(),
      ),
      (route) => false,
    );
  }

  void _handleSignOut() async {
    await context.read<AuthProvider>().signOut();
    
    if (mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => const SignInScreen(),
        ),
        (route) => false,
      );
    }
  }
}
