import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';
import '../../models/chat_message_model.dart';

/// ويدجت الاقتراحات السريعة
class QuickSuggestionsWidget extends StatelessWidget {
  final Function(QuickSuggestion) onSuggestionTap;

  const QuickSuggestionsWidget({
    super.key,
    required this.onSuggestionTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: AppColors.border,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اقتراحات سريعة',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          _buildSuggestionCategory(
            'استشارات طبية',
            QuickSuggestions.medical,
            AppColors.primary,
          ),
          const SizedBox(height: 16),
          _buildSuggestionCategory(
            'نصائح عامة',
            QuickSuggestions.general,
            AppColors.secondary,
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionCategory(
    String title,
    List<QuickSuggestion> suggestions,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 4,
              height: 16,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: suggestions.map((suggestion) {
            return _buildSuggestionChip(suggestion, color);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSuggestionChip(QuickSuggestion suggestion, Color color) {
    return GestureDetector(
      onTap: () => onSuggestionTap(suggestion),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              suggestion.icon,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                suggestion.text,
                style: AppTextStyles.bodySmall.copyWith(
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
