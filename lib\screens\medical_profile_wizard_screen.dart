import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/medical_profile_model.dart';
import '../providers/medical_profile_provider.dart';
import '../theme/app_styles.dart';
import '../config/app_config.dart';
import '../widgets/medical_profile_steps/step1_personal_info.dart';
import '../widgets/medical_profile_steps/step2_medical_condition.dart';
import '../widgets/medical_profile_steps/step3_prosthetic_info.dart';
import '../widgets/medical_profile_steps/step4_medical_attachments.dart';
import '../widgets/medical_profile_steps/step5_location_info.dart';
import '../widgets/medical_profile_steps/step6_final_consent.dart';
import 'home_screen.dart';

/// Medical Profile Wizard Screen
/// Multi-step form for collecting patient medical information
class MedicalProfileWizardScreen extends StatefulWidget {
  const MedicalProfileWizardScreen({super.key});

  @override
  State<MedicalProfileWizardScreen> createState() =>
      _MedicalProfileWizardScreenState();
}

class _MedicalProfileWizardScreenState
    extends State<MedicalProfileWizardScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  bool _isLoading = false;

  final List<String> _stepTitles = [
    'المعلومات الشخصية',
    'الوضع الصحي',
    'الطرف الاصطناعي',
    'المرفقات الطبية',
    'العنوان والموقع',
    'الموافقة النهائية',
  ];

  final List<String> _stepDescriptions = [
    'الاسم والجنس وتاريخ الميلاد',
    'نوع الإعاقة وتفاصيل الحالة',
    'معلومات الطرف الاصطناعي',
    'رفع التقارير والصور الطبية',
    'الولاية والمدينة والعنوان',
    'الموافقة على الشروط',
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            _buildProgressIndicator(),
            _buildStepHeader(),
            Expanded(child: _buildStepContent()),
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.textOnPrimary,
      title: const Text(
        'إكمال الملف الطبي',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      ),
      leading: _currentStep > 0
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: _previousStep,
            )
          : null,
      actions: [
        TextButton(
          onPressed: _skipToHome,
          child: Text(
            'تخطي',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textOnPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    final provider = context.watch<MedicalProfileProvider>();

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: Column(
        children: [
          // Step indicators
          Row(
            children: List.generate(_stepTitles.length, (index) {
              final isActive = index == _currentStep;
              final isCompleted = _isStepCompleted(index, provider.profile);
              final isPassed = index < _currentStep;

              return Expanded(
                child: Container(
                  height: 6,
                  margin: EdgeInsets.only(
                    right: index == 0 ? 0 : 2,
                    left: index == _stepTitles.length - 1 ? 0 : 2,
                  ),
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? AppColors.success
                        : isActive
                        ? AppColors.primary
                        : isPassed
                        ? AppColors.warning
                        : AppColors.border,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: AppDimensions.marginSmall),
          // Step counter with validation status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الخطوة ${_currentStep + 1} من ${_stepTitles.length}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              if (_isStepCompleted(_currentStep, provider.profile))
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 16,
                      color: AppColors.success,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'مكتملة',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ],
      ),
    );
  }

  bool _isStepCompleted(int stepIndex, MedicalProfile profile) {
    switch (stepIndex) {
      case 0:
        return profile.isStep1Valid();
      case 1:
        return profile.isStep2Valid();
      case 2:
        return profile.isStep3Valid();
      case 3:
        return true; // Optional step
      case 4:
        return profile.isStep5Valid();
      case 5:
        return profile.isStep6Valid();
      default:
        return false;
    }
  }

  bool _canProceedToNext() {
    final provider = context.read<MedicalProfileProvider>();
    return _validateCurrentStep(provider.profile);
  }

  Widget _buildStepHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingLarge,
        vertical: AppDimensions.paddingMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            _stepTitles[_currentStep],
            style: AppTextStyles.headline3.copyWith(color: AppColors.primary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.marginSmall),
          Text(
            _stepDescriptions[_currentStep],
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent() {
    return PageView(
      controller: _pageController,
      physics: const NeverScrollableScrollPhysics(), // منع السحب
      onPageChanged: (index) {
        setState(() {
          _currentStep = index;
        });
      },
      children: const [
        Step1PersonalInfo(),
        Step2MedicalCondition(),
        Step3ProstheticInfo(),
        Step4MedicalAttachments(),
        Step5LocationInfo(),
        Step6FinalConsent(),
      ],
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(top: BorderSide(color: AppColors.border)),
      ),
      child: Row(
        children: [
          // Previous button
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _isLoading ? null : _previousStep,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: BorderSide(color: AppColors.primary),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      AppDimensions.radiusMedium,
                    ),
                  ),
                  minimumSize: const Size(0, AppDimensions.buttonHeight),
                ),
                child: const Text('السابق'),
              ),
            ),

          if (_currentStep > 0)
            const SizedBox(width: AppDimensions.marginMedium),

          // Next/Complete button
          Expanded(
            flex: _currentStep == 0 ? 1 : 1,
            child: ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : _canProceedToNext()
                  ? _nextStep
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _canProceedToNext()
                    ? AppColors.primary
                    : AppColors.border,
                foregroundColor: _canProceedToNext()
                    ? AppColors.textOnPrimary
                    : AppColors.textSecondary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusMedium,
                  ),
                ),
                minimumSize: const Size(0, AppDimensions.buttonHeight),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _currentStep == _stepTitles.length - 1
                          ? 'إكمال الملف'
                          : 'التالي',
                    ),
            ),
          ),
        ],
      ),
    );
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: AppConfig.shortAnimationDuration,
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextStep() async {
    final provider = context.read<MedicalProfileProvider>();

    // Validate current step
    if (!_validateCurrentStep(provider.profile)) {
      _showValidationError();
      return;
    }

    if (_currentStep < _stepTitles.length - 1) {
      // Move to next step
      _pageController.nextPage(
        duration: AppConfig.shortAnimationDuration,
        curve: Curves.easeInOut,
      );
    } else {
      // Complete profile
      await _completeProfile();
    }
  }

  bool _validateCurrentStep(MedicalProfile profile) {
    switch (_currentStep) {
      case 0:
        return profile.isStep1Valid();
      case 1:
        return profile.isStep2Valid();
      case 2:
        return profile.isStep3Valid();
      case 3:
        return true; // Optional step
      case 4:
        return profile.isStep5Valid();
      case 5:
        return profile.isStep6Valid();
      default:
        return false;
    }
  }

  void _showValidationError() {
    String message = _getValidationMessage();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'موافق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  String _getValidationMessage() {
    switch (_currentStep) {
      case 0:
        return 'يرجى إكمال: الاسم الكامل، الجنس، تاريخ الميلاد، ورقم الهاتف';
      case 1:
        return 'يرجى اختيار نوع الإعاقة';
      case 2:
        return 'يرجى تحديد نوع الطرف الاصطناعي إذا كنت تستخدم واحداً';
      case 4:
        return 'يرجى اختيار الولاية والبلدية';
      case 5:
        return 'يرجى الموافقة على الشروط والأحكام';
      default:
        return 'يرجى إكمال جميع الحقول المطلوبة';
    }
  }

  bool _validateAllSteps(MedicalProfile profile) {
    return profile.isStep1Valid() &&
        profile.isStep2Valid() &&
        profile.isStep3Valid() &&
        profile.isStep5Valid() &&
        profile.isStep6Valid();
  }

  void _showFinalValidationError() {
    final provider = context.read<MedicalProfileProvider>();
    final profile = provider.profile;

    List<String> missingSteps = [];

    if (!profile.isStep1Valid()) {
      missingSteps.add('المعلومات الشخصية');
    }
    if (!profile.isStep2Valid()) {
      missingSteps.add('نوع الإعاقة');
    }
    if (!profile.isStep3Valid()) {
      missingSteps.add('معلومات الطرف الاصطناعي');
    }
    if (!profile.isStep5Valid()) {
      missingSteps.add('معلومات الموقع');
    }
    if (!profile.isStep6Valid()) {
      missingSteps.add('الموافقة على الشروط');
    }

    String message =
        'يرجى إكمال الخطوات التالية:\n• ${missingSteps.join('\n• ')}';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'بيانات غير مكتملة',
          style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.error),
        ),
        content: Text(message, style: AppTextStyles.bodyMedium),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('موافق', style: TextStyle(color: AppColors.primary)),
          ),
        ],
      ),
    );
  }

  Future<void> _completeProfile() async {
    final provider = context.read<MedicalProfileProvider>();

    // Final validation check
    if (!_validateAllSteps(provider.profile)) {
      _showFinalValidationError();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await provider.completeProfile();

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم إكمال الملف الطبي بنجاح!'),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Navigate to home screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _skipToHome() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تخطي إكمال الملف؟'),
        content: const Text(
          'يمكنك إكمال الملف الطبي لاحقاً من الإعدادات.\n'
          'هل تريد المتابعة إلى الصفحة الرئيسية؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const HomeScreen()),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary),
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }
}
