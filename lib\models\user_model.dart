/// User Model for Khotwa App
/// Represents user data structure for patients
/// Following the app requirements for patient-only registration

class UserModel {
  final String id;
  final String email;
  final String fullName;
  final String userType;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isEmailVerified;
  final String? phoneNumber;
  final String? profileImageUrl;
  final UserProfile? profile;

  UserModel({
    required this.id,
    required this.email,
    required this.fullName,
    required this.userType,
    required this.createdAt,
    this.updatedAt,
    this.isEmailVerified = false,
    this.phoneNumber,
    this.profileImageUrl,
    this.profile,
  });

  /// Create UserModel from Supabase User
  factory UserModel.fromSupabaseUser(dynamic user) {
    final metadata = user.userMetadata ?? <String, dynamic>{};
    
    return UserModel(
      id: user.id,
      email: user.email ?? '',
      fullName: metadata['full_name'] ?? '',
      userType: metadata['user_type'] ?? 'patient',
      createdAt: user.createdAt ?? DateTime.now(),
      updatedAt: user.updatedAt,
      isEmailVerified: user.emailConfirmedAt != null,
      phoneNumber: metadata['phone_number'],
      profileImageUrl: metadata['profile_image_url'],
    );
  }

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      fullName: json['full_name'] ?? '',
      userType: json['user_type'] ?? 'patient',
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      isEmailVerified: json['is_email_verified'] ?? false,
      phoneNumber: json['phone_number'],
      profileImageUrl: json['profile_image_url'],
      profile: json['profile'] != null 
          ? UserProfile.fromJson(json['profile']) 
          : null,
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'user_type': userType,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_email_verified': isEmailVerified,
      'phone_number': phoneNumber,
      'profile_image_url': profileImageUrl,
      'profile': profile?.toJson(),
    };
  }

  /// Create a copy with updated fields
  UserModel copyWith({
    String? id,
    String? email,
    String? fullName,
    String? userType,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    String? phoneNumber,
    String? profileImageUrl,
    UserProfile? profile,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      userType: userType ?? this.userType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      profile: profile ?? this.profile,
    );
  }

  /// Get display name (first name or full name)
  String get displayName {
    if (fullName.isNotEmpty) {
      final parts = fullName.split(' ');
      return parts.isNotEmpty ? parts.first : fullName;
    }
    return email.split('@').first;
  }

  /// Get initials for avatar
  String get initials {
    if (fullName.isNotEmpty) {
      final parts = fullName.split(' ');
      if (parts.length >= 2) {
        return '${parts.first[0]}${parts[1][0]}'.toUpperCase();
      } else if (parts.isNotEmpty) {
        return parts.first.substring(0, 1).toUpperCase();
      }
    }
    return email.substring(0, 1).toUpperCase();
  }

  /// Check if user is a patient
  bool get isPatient => userType == 'patient';

  /// Check if user is a specialist
  bool get isSpecialist => userType == 'specialist';

  /// Check if user is an admin
  bool get isAdmin => userType == 'admin';

  /// Check if profile is complete
  bool get isProfileComplete {
    return fullName.isNotEmpty && 
           isEmailVerified && 
           profile != null && 
           profile!.isComplete;
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, fullName: $fullName, userType: $userType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// User Profile Model for additional patient information
class UserProfile {
  final String? dateOfBirth;
  final String? gender;
  final String? city;
  final String? address;
  final String? emergencyContact;
  final String? emergencyContactPhone;
  final List<String>? medicalConditions;
  final List<String>? medications;
  final String? notes;

  UserProfile({
    this.dateOfBirth,
    this.gender,
    this.city,
    this.address,
    this.emergencyContact,
    this.emergencyContactPhone,
    this.medicalConditions,
    this.medications,
    this.notes,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      dateOfBirth: json['date_of_birth'],
      gender: json['gender'],
      city: json['city'],
      address: json['address'],
      emergencyContact: json['emergency_contact'],
      emergencyContactPhone: json['emergency_contact_phone'],
      medicalConditions: json['medical_conditions'] != null
          ? List<String>.from(json['medical_conditions'])
          : null,
      medications: json['medications'] != null
          ? List<String>.from(json['medications'])
          : null,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date_of_birth': dateOfBirth,
      'gender': gender,
      'city': city,
      'address': address,
      'emergency_contact': emergencyContact,
      'emergency_contact_phone': emergencyContactPhone,
      'medical_conditions': medicalConditions,
      'medications': medications,
      'notes': notes,
    };
  }

  UserProfile copyWith({
    String? dateOfBirth,
    String? gender,
    String? city,
    String? address,
    String? emergencyContact,
    String? emergencyContactPhone,
    List<String>? medicalConditions,
    List<String>? medications,
    String? notes,
  }) {
    return UserProfile(
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      city: city ?? this.city,
      address: address ?? this.address,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyContactPhone: emergencyContactPhone ?? this.emergencyContactPhone,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      medications: medications ?? this.medications,
      notes: notes ?? this.notes,
    );
  }

  /// Check if profile has basic required information
  bool get isComplete {
    return dateOfBirth != null && 
           gender != null && 
           city != null;
  }

  /// Get age from date of birth
  int? get age {
    if (dateOfBirth == null) return null;
    final birthDate = DateTime.tryParse(dateOfBirth!);
    if (birthDate == null) return null;
    
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  @override
  String toString() {
    return 'UserProfile(gender: $gender, city: $city, age: $age)';
  }
}
