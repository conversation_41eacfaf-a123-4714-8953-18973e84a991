import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

/// Authentication Service for Khotwa App
/// Handles all authentication operations using Supabase
/// Supports patient-only registration as per requirements

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  late final SupabaseClient _supabase;
  bool _isInitialized = false;

  /// Initialize Supabase client
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Check if Supabase is properly configured
    if (AppConfig.supabaseUrl.contains('your-project') ||
        AppConfig.supabaseAnonKey.contains('your-anon-key') ||
        AppConfig.supabaseAnonKey.contains('placeholder')) {
      throw Exception(
        'Supabase غير مُعد بشكل صحيح!\n'
        'يرجى اتباع التعليمات في ملف GET_SUPABASE_KEYS.md\n'
        'للحصول على المفاتيح الصحيحة من Supabase Dashboard',
      );
    }

    try {
      // Check if Supabase is already initialized globally
      _supabase = Supabase.instance.client;
      _isInitialized = true;
      if (AppConfig.isDebugMode) {
        debugPrint('Supabase was already initialized, using existing instance');
      }
      return;
    } catch (e) {
      // Supabase not initialized yet, continue with initialization
    }

    try {
      await Supabase.initialize(
        url: AppConfig.supabaseUrl,
        anonKey: AppConfig.supabaseAnonKey,
      );

      _supabase = Supabase.instance.client;
      _isInitialized = true;

      // Test the connection
      final session = _supabase.auth.currentSession;
      if (AppConfig.isDebugMode) {
        debugPrint(
          'Supabase initialized successfully. Session: ${session != null ? 'Active' : 'None'}',
        );
      }
    } catch (e) {
      if (AppConfig.isDebugMode) {
        debugPrint('Supabase initialization error: $e');
        debugPrint('URL: ${AppConfig.supabaseUrl}');
        debugPrint('Key: ${AppConfig.supabaseAnonKey.substring(0, 20)}...');
      }

      // Handle the "already initialized" error
      if (e.toString().contains('already initialized')) {
        _supabase = Supabase.instance.client;
        _isInitialized = true;
        if (AppConfig.isDebugMode) {
          debugPrint(
            'Supabase was already initialized, using existing instance',
          );
        }
        return;
      }

      // Provide helpful error message
      if (e.toString().contains('invalid api key') ||
          e.toString().contains('Invalid API key')) {
        throw Exception(
          'مفتاح Supabase غير صحيح!\n'
          'يرجى التحقق من المفاتيح في ملف app_config.dart\n'
          'واتباع التعليمات في GET_SUPABASE_KEYS.md',
        );
      }

      rethrow;
    }
  }

  /// Get current user
  User? get currentUser => _supabase.auth.currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  /// Get current session
  Session? get currentSession => _supabase.auth.currentSession;

  /// Sign up new patient user
  Future<AuthResult> signUpPatient({
    required String fullName,
    required String email,
    required String password,
  }) async {
    try {
      // Validate input
      if (!AppConfig.isValidEmail(email)) {
        return AuthResult.error(AppConfig.errorInvalidEmail);
      }

      if (!AppConfig.isValidPassword(password)) {
        return AuthResult.error(AppConfig.errorWeakPassword);
      }

      if (!AppConfig.isValidName(fullName)) {
        return AuthResult.error(AppConfig.errorNameTooShort);
      }

      // Sign up with Supabase
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName, 'user_type': AppConfig.userTypePatient},
      );

      if (response.user != null) {
        // Save session locally
        await _saveSession();
        return AuthResult.success(
          user: response.user!,
          message: AppConfig.successAccountCreated,
        );
      } else {
        return AuthResult.error(AppConfig.errorUnknown);
      }
    } on AuthException catch (e) {
      return AuthResult.error(_handleAuthException(e));
    } catch (e) {
      return AuthResult.error(AppConfig.errorUnknown);
    }
  }

  /// Sign in existing user
  Future<AuthResult> signIn({
    required String email,
    required String password,
  }) async {
    try {
      // Validate input
      if (!AppConfig.isValidEmail(email)) {
        return AuthResult.error(AppConfig.errorInvalidEmail);
      }

      if (password.isEmpty) {
        return AuthResult.error(AppConfig.errorWeakPassword);
      }

      // Sign in with Supabase
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Verify user type is patient
        final userType = response.user!.userMetadata?['user_type'];
        if (userType != AppConfig.userTypePatient) {
          await signOut(); // Sign out non-patient users
          return AuthResult.error(
            'غير مسموح لهذا النوع من المستخدمين بالدخول عبر التطبيق',
          );
        }

        // Save session locally
        await _saveSession();
        return AuthResult.success(
          user: response.user!,
          message: AppConfig.successLoginSuccessful,
        );
      } else {
        return AuthResult.error(AppConfig.errorInvalidCredentials);
      }
    } on AuthException catch (e) {
      return AuthResult.error(_handleAuthException(e));
    } catch (e) {
      return AuthResult.error(AppConfig.errorUnknown);
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
      await _clearSession();
    } catch (e) {
      // Silent fail for sign out
    }
  }

  /// Reset password
  Future<AuthResult> resetPassword({required String email}) async {
    try {
      if (!AppConfig.isValidEmail(email)) {
        return AuthResult.error(AppConfig.errorInvalidEmail);
      }

      await _supabase.auth.resetPasswordForEmail(email);
      return AuthResult.success(message: AppConfig.successPasswordResetSent);
    } on AuthException catch (e) {
      return AuthResult.error(_handleAuthException(e));
    } catch (e) {
      return AuthResult.error(AppConfig.errorUnknown);
    }
  }

  /// Resend email verification
  Future<AuthResult> resendEmailVerification() async {
    try {
      if (currentUser?.email == null) {
        return AuthResult.error('لا يوجد مستخدم مسجل دخول');
      }

      await _supabase.auth.resend(
        type: OtpType.signup,
        email: currentUser!.email!,
      );

      return AuthResult.success(
        message: AppConfig.successEmailVerificationSent,
      );
    } on AuthException catch (e) {
      return AuthResult.error(_handleAuthException(e));
    } catch (e) {
      return AuthResult.error(AppConfig.errorUnknown);
    }
  }

  /// Check if email is verified
  bool get isEmailVerified => currentUser?.emailConfirmedAt != null;

  /// Get user display name
  String get userDisplayName {
    if (currentUser == null) return '';
    return currentUser!.userMetadata?['full_name'] ??
        currentUser!.email?.split('@').first ??
        'مستخدم';
  }

  /// Save session to local storage
  Future<void> _saveSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final session = currentSession;
      if (session != null) {
        await prefs.setString(AppConfig.sessionKey, session.accessToken);
        await prefs.setInt(
          '${AppConfig.sessionKey}_expires',
          session.expiresAt ?? 0,
        );
      }
    } catch (e) {
      // Silent fail for session save
    }
  }

  /// Clear session from local storage
  Future<void> _clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConfig.sessionKey);
      await prefs.remove('${AppConfig.sessionKey}_expires');
    } catch (e) {
      // Silent fail for session clear
    }
  }

  /// Check if saved session is valid
  Future<bool> hasValidSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(AppConfig.sessionKey);
      final expiresAt = prefs.getInt('${AppConfig.sessionKey}_expires') ?? 0;

      if (token == null || expiresAt == 0) return false;

      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      return expiresAt > now;
    } catch (e) {
      return false;
    }
  }

  /// Handle Supabase auth exceptions
  String _handleAuthException(AuthException e) {
    switch (e.message.toLowerCase()) {
      case 'invalid login credentials':
        return AppConfig.errorInvalidCredentials;
      case 'user already registered':
        return AppConfig.errorEmailAlreadyExists;
      case 'email not confirmed':
        return 'يرجى تأكيد البريد الإلكتروني أولاً';
      case 'signup disabled':
        return 'تسجيل الحسابات الجديدة معطل مؤقتاً';
      default:
        return e.message.isNotEmpty ? e.message : AppConfig.errorUnknown;
    }
  }

  /// Listen to auth state changes
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;
}

/// Authentication result wrapper
class AuthResult {
  final bool isSuccess;
  final String message;
  final User? user;

  AuthResult._({required this.isSuccess, required this.message, this.user});

  factory AuthResult.success({User? user, String? message}) {
    return AuthResult._(isSuccess: true, message: message ?? '', user: user);
  }

  factory AuthResult.error(String message) {
    return AuthResult._(isSuccess: false, message: message);
  }
}
