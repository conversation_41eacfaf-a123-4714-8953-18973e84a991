# دليل الصور - تطبيق خطوة

هذا المجلد يحتوي على جميع الصور والأيقونات المستخدمة في تطبيق خطوة.

## 📁 هيكل المجلد

```
assets/images/
├── README.md                 # هذا الملف
├── placeholder.png           # صورة افتراضية عامة
├── logo/                     # شعار التطبيق
│   ├── logo.png             # الشعار الأساسي
│   ├── logo_white.png       # الشعار الأبيض
│   └── logo_transparent.png # الشعار الشفاف
├── onboarding/              # صور شاشات التعريف
│   ├── welcome_bg.jpg       # خلفية شاشة الترحيب
│   └── intro_slides/        # صور شرائح التعريف
├── services/                # صور الخدمات
│   ├── prosthetics.jpg      # الأطراف الاصطناعية
│   ├── physiotherapy.jpg    # العلاج الفيزيائي
│   └── rehabilitation.jpg   # التأهيل الحركي
├── icons/                   # أيقونات مخصصة
│   ├── medical.png          # أيقونة طبية
│   ├── appointment.png      # أيقونة الموعد
│   └── consultation.png     # أيقونة الاستشارة
└── backgrounds/             # خلفيات متنوعة
    ├── gradient_bg.png      # خلفية متدرجة
    └── pattern_bg.png       # خلفية بنمط
```

## 🎨 مواصفات الصور

### الشعار (Logo)
- **الأبعاد**: 512x512 بكسل (مربع)
- **التنسيق**: PNG مع خلفية شفافة
- **الاستخدام**: شاشة البداية، شريط التطبيق، متجر التطبيقات

### صور الخدمات
- **الأبعاد**: 400x300 بكسل (نسبة 4:3)
- **التنسيق**: JPG أو PNG
- **الجودة**: عالية (90% أو أكثر)
- **الاستخدام**: عرض الخدمات في الصفحة الرئيسية

### الأيقونات المخصصة
- **الأبعاد**: 64x64 بكسل
- **التنسيق**: PNG مع خلفية شفافة
- **الألوان**: متوافقة مع نظام الألوان للتطبيق
- **الاستخدام**: الأزرار والقوائم

### خلفيات الشاشات
- **الأبعاد**: 1080x1920 بكسل (نسبة 9:16)
- **التنسيق**: JPG للصور الفوتوغرافية، PNG للرسوميات
- **الحجم**: أقل من 500 كيلوبايت
- **الاستخدام**: خلفيات الشاشات المختلفة

## 📐 إرشادات التصميم

### الألوان المفضلة
- **الأساسي**: #2E7D32 (أخضر)
- **الثانوي**: #1976D2 (أزرق)
- **المميز**: #FF9800 (برتقالي)
- **الخلفية**: #FAFAFA (رمادي فاتح)

### الخطوط
- **العربية**: تجوال (Tajawal) أو القاهرة (Cairo)
- **الإنجليزية**: Roboto أو Open Sans

### الأسلوب
- تصميم نظيف وبسيط
- ألوان دافئة ومريحة
- تركيز على إمكانية الوصول
- دعم كامل للتخطيط من اليمين لليسار (RTL)

## 📱 أحجام الأيقونات للمنصات

### Android
- **mdpi**: 48x48 بكسل
- **hdpi**: 72x72 بكسل
- **xhdpi**: 96x96 بكسل
- **xxhdpi**: 144x144 بكسل
- **xxxhdpi**: 192x192 بكسل

### iOS
- **1x**: 29x29 بكسل
- **2x**: 58x58 بكسل
- **3x**: 87x87 بكسل

## 🔧 أدوات التحسين

### ضغط الصور
- استخدم أدوات مثل TinyPNG أو ImageOptim
- احتفظ بجودة عالية مع حجم ملف صغير
- تجنب الصور الكبيرة غير الضرورية

### التنسيقات المدعومة
- **PNG**: للصور مع الشفافية والأيقونات
- **JPG**: للصور الفوتوغرافية
- **SVG**: للأيقونات البسيطة (إذا أمكن)
- **WebP**: للصور المحسنة (اختياري)

## 📋 قائمة التحقق

عند إضافة صورة جديدة، تأكد من:

- [ ] الأبعاد صحيحة حسب الاستخدام
- [ ] التنسيق مناسب (PNG/JPG/SVG)
- [ ] الحجم محسن (أقل من الحد المطلوب)
- [ ] الألوان متوافقة مع نظام التطبيق
- [ ] الصورة واضحة وعالية الجودة
- [ ] اسم الملف وصفي ومفهوم
- [ ] لا توجد حقوق طبع ونشر مخالفة

## 🎯 أفضل الممارسات

1. **استخدم أسماء ملفات وصفية**
   - `prosthetic_arm.jpg` بدلاً من `image1.jpg`

2. **نظم الملفات في مجلدات فرعية**
   - سهل العثور على الصور وإدارتها

3. **احتفظ بنسخ أصلية**
   - احفظ النسخ الأصلية في مجلد منفصل

4. **اختبر على أجهزة مختلفة**
   - تأكد من وضوح الصور على جميع الأحجام

5. **راعي إمكانية الوصول**
   - استخدم نصوص بديلة وصفية
   - تباين ألوان جيد

## 📞 التواصل

إذا كنت بحاجة إلى مساعدة في تحسين الصور أو لديك أسئلة حول المواصفات:

- فريق التصميم: <EMAIL>
- فريق التطوير: <EMAIL>

---

**آخر تحديث**: يوليو 2025  
**الإصدار**: 1.0.0
