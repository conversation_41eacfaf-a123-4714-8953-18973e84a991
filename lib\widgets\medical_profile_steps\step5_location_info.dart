import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/medical_profile_provider.dart';
import '../../services/location_service.dart';
import '../../theme/app_styles.dart';

/// Step 5: Location Information
/// Collects user's location information
class Step5LocationInfo extends StatefulWidget {
  const Step5LocationInfo({super.key});

  @override
  State<Step5LocationInfo> createState() => _Step5LocationInfoState();
}

class _Step5LocationInfoState extends State<Step5LocationInfo> {
  final _addressController = TextEditingController();

  String? _selectedState;
  String? _selectedCity;
  bool _shareLocation = false;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    final provider = context.read<MedicalProfileProvider>();
    final profile = provider.profile;

    _selectedState = profile.state;
    _selectedCity = profile.city;
    _addressController.text = profile.address ?? '';
    _shareLocation = profile.shareLocation;
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildStateField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildCityField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildAddressField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildLocationSharingField(),
            const SizedBox(height: AppDimensions.marginXLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.location_on_outlined,
            color: AppColors.primary,
            size: AppDimensions.iconMedium,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Text(
              'معلومات الموقع تساعدنا في العثور على أقرب المراكز الطبية والمختصين لك.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStateField() {
    final provider = context.read<MedicalProfileProvider>();
    final states = provider.getAlgerianStates();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الولاية *',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            color: AppColors.surface,
          ),
          child: DropdownButtonFormField<String>(
            value: states.contains(_selectedState) ? _selectedState : null,
            decoration: const InputDecoration(
              prefixIcon: Icon(Icons.map_outlined),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingMedium,
                vertical: AppDimensions.paddingMedium,
              ),
            ),
            hint: const Text('اختر الولاية'),
            items: states.map((state) {
              return DropdownMenuItem<String>(
                value: state,
                child: Text(state, style: AppTextStyles.bodyMedium),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedState = value;
                _selectedCity = null; // Reset city when state changes
              });
              _updateLocationInfo();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCityField() {
    final provider = context.read<MedicalProfileProvider>();
    final cities = _selectedState != null
        ? provider.getCitiesForState(_selectedState!)
        : <String>[];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'البلدية *',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            color: AppColors.surface,
          ),
          child: DropdownButtonFormField<String>(
            value: cities.contains(_selectedCity) ? _selectedCity : null,
            decoration: const InputDecoration(
              prefixIcon: Icon(Icons.location_city_outlined),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingMedium,
                vertical: AppDimensions.paddingMedium,
              ),
            ),
            hint: Text(
              _selectedState != null ? 'اختر البلدية' : 'اختر الولاية أولاً',
            ),
            items: cities.map((city) {
              return DropdownMenuItem<String>(
                value: city,
                child: Text(city, style: AppTextStyles.bodyMedium),
              );
            }).toList(),
            onChanged: _selectedState != null
                ? (value) {
                    setState(() {
                      _selectedCity = value;
                    });
                    _updateLocationInfo();
                  }
                : null,
          ),
        ),
      ],
    );
  }

  Widget _buildAddressField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العنوان التفصيلي (اختياري)',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        TextFormField(
          controller: _addressController,
          textDirection: TextDirection.rtl,
          maxLines: 2,
          decoration: InputDecoration(
            hintText: 'الحي، الشارع، رقم المبنى...',
            prefixIcon: const Padding(
              padding: EdgeInsets.only(bottom: 20),
              child: Icon(Icons.home_outlined),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          onChanged: (_) => _updateLocationInfo(),
        ),
      ],
    );
  }

  Widget _buildLocationSharingField() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Icon(
            Icons.my_location_outlined,
            color: AppColors.primary,
            size: AppDimensions.iconMedium,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تحديد الموقع التلقائي',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  'اضغط لتحديد الولاية والبلدية تلقائياً',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _shareLocation,
            onChanged: (value) async {
              if (value) {
                // When enabling location sharing, get current location
                await _getCurrentLocation();
              } else {
                setState(() {
                  _shareLocation = value;
                });
                _updateLocationInfo();
              }
            },
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Get current position
      final position = await LocationService.getCurrentPosition();
      if (position == null) {
        _showLocationError('فشل في الحصول على الموقع الحالي');
        return;
      }

      // Get address from coordinates
      final addressInfo = await LocationService.getAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (addressInfo != null && mounted) {
        final provider = context.read<MedicalProfileProvider>();
        final availableStates = provider.getAlgerianStates();

        // Validate that the detected state exists in our list
        String? validState = addressInfo['state'];
        if (validState != null && !availableStates.contains(validState)) {
          // Try to find a close match or use first available state
          validState =
              _findClosestMatch(validState, availableStates) ??
              availableStates.first;
        }

        // Validate that the detected city exists for the selected state
        String? validCity = addressInfo['city'];
        if (validState != null && validCity != null) {
          final availableCities = provider.getCitiesForState(validState);
          if (availableCities.isNotEmpty &&
              !availableCities.contains(validCity)) {
            validCity =
                _findClosestMatch(validCity, availableCities) ??
                availableCities.first;
          }
        }

        setState(() {
          _selectedState = validState;
          _selectedCity = validCity;
          _addressController.text = addressInfo['address'] ?? '';
          _shareLocation = true;
        });

        _updateLocationInfo();

        // Show success message
        if (mounted) {
          String message = 'تم تحديد الموقع بنجاح';
          if (validState != null) {
            message += ': $validState';
            if (validCity != null) {
              message += ', $validCity';
            }
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } else {
        _showLocationError('فشل في تحديد العنوان من الموقع');
      }
    } catch (e) {
      _showLocationError('حدث خطأ أثناء تحديد الموقع: ${e.toString()}');
    }
  }

  void _showLocationError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          action: SnackBarAction(
            label: 'الإعدادات',
            textColor: Colors.white,
            onPressed: () async {
              await LocationService.openAppSettings();
            },
          ),
        ),
      );
    }
  }

  /// Find the closest match for a given text in a list of options
  String? _findClosestMatch(String target, List<String> options) {
    if (options.isEmpty) return null;

    target = target.toLowerCase().trim();

    // First, try exact match (case insensitive)
    for (String option in options) {
      if (option.toLowerCase() == target) {
        return option;
      }
    }

    // Then, try partial match (contains)
    for (String option in options) {
      if (option.toLowerCase().contains(target) ||
          target.contains(option.toLowerCase())) {
        return option;
      }
    }

    // If no match found, return null
    return null;
  }

  void _updateLocationInfo() {
    final provider = context.read<MedicalProfileProvider>();
    provider.updateLocationInfo(
      state: _selectedState,
      city: _selectedCity,
      address: _addressController.text.trim(),
      shareLocation: _shareLocation,
    );
  }
}
