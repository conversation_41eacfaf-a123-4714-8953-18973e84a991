/// نموذج رسالة المحادثة
class ChatMessage {
  final String id;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final MessageStatus status;

  ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.status = MessageStatus.sent,
  });

  ChatMessage copyWith({
    String? id,
    String? content,
    bool? isUser,
    DateTime? timestamp,
    MessageStatus? status,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      content: json['content'],
      isUser: json['isUser'],
      timestamp: DateTime.parse(json['timestamp']),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
    );
  }
}

/// حالة الرسالة
enum MessageStatus {
  sending,
  sent,
  error,
}

/// نموذج الاقتراحات السريعة
class QuickSuggestion {
  final String id;
  final String text;
  final String icon;
  final String category;

  const QuickSuggestion({
    required this.id,
    required this.text,
    required this.icon,
    required this.category,
  });
}

/// الاقتراحات السريعة المحددة مسبقاً
class QuickSuggestions {
  static const List<QuickSuggestion> medical = [
    QuickSuggestion(
      id: 'symptoms',
      text: 'أشعر بأعراض غريبة، ما رأيك؟',
      icon: '🩺',
      category: 'medical',
    ),
    QuickSuggestion(
      id: 'medication',
      text: 'متى يجب أن آخذ دوائي؟',
      icon: '💊',
      category: 'medical',
    ),
    QuickSuggestion(
      id: 'appointment',
      text: 'أحتاج لحجز موعد طبي',
      icon: '📅',
      category: 'medical',
    ),
    QuickSuggestion(
      id: 'emergency',
      text: 'هذه حالة طارئة، ماذا أفعل؟',
      icon: '🚨',
      category: 'medical',
    ),
  ];

  static const List<QuickSuggestion> general = [
    QuickSuggestion(
      id: 'health_tips',
      text: 'أعطني نصائح صحية يومية',
      icon: '💡',
      category: 'general',
    ),
    QuickSuggestion(
      id: 'diet',
      text: 'ما هو النظام الغذائي المناسب لي؟',
      icon: '🥗',
      category: 'general',
    ),
    QuickSuggestion(
      id: 'exercise',
      text: 'اقترح لي تمارين مناسبة',
      icon: '🏃‍♂️',
      category: 'general',
    ),
    QuickSuggestion(
      id: 'sleep',
      text: 'كيف أحسن من جودة نومي؟',
      icon: '😴',
      category: 'general',
    ),
  ];

  static List<QuickSuggestion> get all => [...medical, ...general];
}
