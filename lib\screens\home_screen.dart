import 'package:flutter/material.dart';
import '../theme/app_styles.dart';

import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/navigation/bottom_nav_bar.dart';

/// Home Screen - الصفحة الرئيسية الاحترافية
///
/// الصفحة الرئيسية المتقدمة للمرضى مع:
/// - سلايدر تحفيزي
/// - المساعد الذكي (AI)
/// - خدمات سريعة
/// - إحصائيات شخصية
/// - إشعارات وتذكيرات
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        bottomNavigationBar: BottomNavBar(
          currentIndex: 0, // الصفحة الرئيسية
          onTap: _handleBottomNavTap,
        ),
        body: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: CustomScrollView(
                  physics: const BouncingScrollPhysics(),
                  slivers: [
                    _buildCustomAppBar(),
                    SliverSafeArea(
                      top:
                          false, // لا نريد SafeArea في الأعلى لأن AppBar يتولى ذلك
                      sliver: SliverPadding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingMedium,
                        ),
                        sliver: SliverList(
                          delegate: SliverChildListDelegate([
                            const SizedBox(height: AppDimensions.marginMedium),

                            // السلايدر التحفيزي - مخفي حسب طلب المستخدم
                            // const MotivationalSlider(),
                            // const SizedBox(height: AppDimensions.marginMedium),

                            // المساعد الذكي - أهم مكون
                            // const SmartAssistantCard(),
                            // const SizedBox(height: AppDimensions.marginMedium),

                            // الخدمات السريعة
                            const QuickServicesGrid(),
                            const SizedBox(height: AppDimensions.marginMedium),

                            // الإحصائيات الشخصية
                            const PersonalStatsSection(),
                            const SizedBox(height: AppDimensions.marginMedium),

                            // الإشعارات والتذكيرات
                            const NotificationsSection(),
                            const SizedBox(
                              height: AppDimensions.marginLarge * 2,
                            ),
                          ]),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return SliverAppBar(
      expandedHeight: kToolbarHeight, // جعل الارتفاع الموسع مثل المصغر
      floating: true,
      pinned: true,
      snap: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      automaticallyImplyLeading: false,
      toolbarHeight: kToolbarHeight,
      // إضافة هذه الخصائص لضمان عدم وجود مساحات إضافية
      forceElevated: false,
      stretch: false,
      // عرض المحتوى في title مباشرة
      title: Row(
        children: [
          Text(
            'خطوة',
            style: AppTextStyles.headline4.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          // أيقونة الإشعارات
          IconButton(
            onPressed: () {
              // TODO: Navigate to notifications
            },
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.textOnPrimary.withValues(alpha: 0.2),
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textOnPrimary,
                    size: 18,
                  ),
                  Positioned(
                    right: 6,
                    top: 6,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: AppColors.error,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 2), // مسافة صغيرة جداً بين الأيقونات
          // أيقونة الملف الشخصي
          IconButton(
            onPressed: () {
              // TODO: Navigate to profile
            },
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            icon: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.textOnPrimary.withValues(alpha: 0.2),
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                Icons.person,
                color: AppColors.textOnPrimary,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleBottomNavTap(int index) {
    switch (index) {
      case 0:
        // الرئيسية - نحن بالفعل في الصفحة الرئيسية
        break;
      case 1:
        // المراكز الطبية
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('سيتم فتح صفحة المراكز الطبية قريباً...'),
            backgroundColor: AppColors.primary,
            behavior: SnackBarBehavior.floating,
          ),
        );
        break;
      case 2:
        // المواعيد
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('سيتم فتح صفحة المواعيد قريباً...'),
            backgroundColor: AppColors.primary,
            behavior: SnackBarBehavior.floating,
          ),
        );
        break;
      case 3:
        // الاستشارات
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('سيتم فتح صفحة الاستشارات قريباً...'),
            backgroundColor: AppColors.primary,
            behavior: SnackBarBehavior.floating,
          ),
        );
        break;
      case 4:
        // المساعد الذكي
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('سيتم فتح المساعد الذكي قريباً...'),
            backgroundColor: AppColors.primary,
            behavior: SnackBarBehavior.floating,
          ),
        );
        break;
    }
  }
}
