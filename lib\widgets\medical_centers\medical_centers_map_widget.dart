import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../../theme/app_styles.dart';
import '../../models/medical_center_model.dart';
import '../../services/medical_centers_service.dart';

/// ويدجت خريطة المراكز الطبية
/// ملاحظة: هذا تطبيق مبسط للخريطة. في التطبيق الحقيقي، استخدم Google Maps
class MedicalCentersMapWidget extends StatelessWidget {
  final List<MedicalCenter> medicalCenters;
  final Position? userPosition;
  final bool isLoading;

  const MedicalCentersMapWidget({
    super.key,
    required this.medicalCenters,
    this.userPosition,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingWidget();
    }

    return Column(
      children: [
        _buildMapPlaceholder(context),
        Expanded(
          child: _buildCentersList(),
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildMapPlaceholder(BuildContext context) {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border,
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // خلفية الخريطة
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade50,
                  Colors.green.shade50,
                ],
              ),
            ),
          ),
          
          // نقاط المراكز الطبية
          ...medicalCenters.asMap().entries.map((entry) {
            final index = entry.key;
            final center = entry.value;
            return Positioned(
              left: 50.0 + (index * 30.0) % 120,
              top: 50.0 + (index * 25.0) % 100,
              child: _buildMapMarker(center),
            );
          }).toList(),
          
          // موقع المستخدم
          if (userPosition != null)
            const Positioned(
              left: 90,
              top: 90,
              child: Icon(
                Icons.my_location,
                color: Colors.blue,
                size: 24,
              ),
            ),
          
          // رسالة توضيحية
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'خريطة تفاعلية - اضغط على أي مركز للمزيد من التفاصيل',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          
          // زر فتح في خرائط جوجل
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                onPressed: () => _openInGoogleMaps(context),
                icon: const Icon(
                  Icons.open_in_new,
                  color: Colors.white,
                  size: 20,
                ),
                tooltip: 'فتح في خرائط جوجل',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapMarker(MedicalCenter center) {
    return GestureDetector(
      onTap: () => _showCenterDetails(center),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: _getMarkerColor(center.type),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.white,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            center.typeIcon,
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }

  Widget _buildCentersList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: medicalCenters.length,
      itemBuilder: (context, index) {
        final center = medicalCenters[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getMarkerColor(center.type).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  center.typeIcon,
                  style: const TextStyle(fontSize: 20),
                ),
              ),
            ),
            title: Text(
              center.name,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  center.typeNameArabic,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _getMarkerColor(center.type),
                  ),
                ),
                if (center.distanceFromUser != null)
                  Text(
                    center.distanceText,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: () => _makePhoneCall(center),
                  icon: Icon(
                    Icons.phone,
                    color: AppColors.primary,
                    size: 20,
                  ),
                  tooltip: 'اتصال',
                ),
                IconButton(
                  onPressed: () => _openCenterInMaps(center),
                  icon: Icon(
                    Icons.directions,
                    color: AppColors.secondary,
                    size: 20,
                  ),
                  tooltip: 'الاتجاهات',
                ),
              ],
            ),
            onTap: () => _showCenterDetails(center),
          ),
        );
      },
    );
  }

  Color _getMarkerColor(String type) {
    switch (type) {
      case 'hospital':
        return Colors.red;
      case 'clinic':
        return Colors.blue;
      case 'pharmacy':
        return Colors.green;
      case 'laboratory':
        return Colors.purple;
      case 'emergency':
        return Colors.orange;
      default:
        return AppColors.primary;
    }
  }

  void _showCenterDetails(MedicalCenter center) {
    // يمكن إضافة نافذة منبثقة لعرض تفاصيل المركز
    debugPrint('عرض تفاصيل المركز: ${center.name}');
  }

  void _makePhoneCall(MedicalCenter center) async {
    try {
      await MedicalCentersService.makePhoneCall(center.phone);
    } catch (e) {
      debugPrint('خطأ في الاتصال: $e');
    }
  }

  void _openCenterInMaps(MedicalCenter center) async {
    try {
      await MedicalCentersService.openInMaps(
        center.latitude,
        center.longitude,
        center.name,
      );
    } catch (e) {
      debugPrint('خطأ في فتح الخرائط: $e');
    }
  }

  void _openInGoogleMaps(BuildContext context) async {
    if (userPosition != null) {
      try {
        await MedicalCentersService.openInMaps(
          userPosition!.latitude,
          userPosition!.longitude,
          'موقعي الحالي',
        );
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في فتح الخرائط: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }
}
