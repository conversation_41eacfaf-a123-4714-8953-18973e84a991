import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../data/profile_service.dart';

/// Profile Provider for managing user profile state
/// Handles profile data, medical centers, and appointments
class ProfileProvider extends ChangeNotifier {
  final ProfileService _profileService = ProfileService();

  // Profile state
  UserProfile? _userProfile;
  bool _isLoading = false;
  String _errorMessage = '';

  // Medical centers and specialists
  List<Map<String, dynamic>> _medicalCenters = [];
  List<Map<String, dynamic>> _specialists = [];
  List<String> _availableCities = [];
  List<String> _availableServices = [];

  // Appointments
  List<Map<String, dynamic>> _appointments = [];

  // Getters
  UserProfile? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  List<Map<String, dynamic>> get medicalCenters => _medicalCenters;
  List<Map<String, dynamic>> get specialists => _specialists;
  List<String> get availableCities => _availableCities;
  List<String> get availableServices => _availableServices;
  List<Map<String, dynamic>> get appointments => _appointments;

  /// Load current user profile
  Future<void> loadUserProfile() async {
    _setLoading(true);
    _clearError();

    try {
      _userProfile = await _profileService.getCurrentUserProfile();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الملف الشخصي');
    } finally {
      _setLoading(false);
    }
  }

  /// Update user profile
  Future<bool> updateProfile(UserProfile profile) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _profileService.updateProfile(profile);
      if (success) {
        _userProfile = profile;
        notifyListeners();
        return true;
      } else {
        _setError('فشل في تحديث الملف الشخصي');
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء التحديث');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update specific profile field
  Future<bool> updateProfileField(String field, dynamic value) async {
    try {
      final success = await _profileService.updateProfileField(field, value);
      if (success) {
        // Reload profile to get updated data
        await loadUserProfile();
        return true;
      }
      return false;
    } catch (e) {
      _setError('فشل في تحديث البيانات');
      return false;
    }
  }

  /// Load medical centers
  Future<void> loadMedicalCenters({String? city, List<String>? services}) async {
    _setLoading(true);
    _clearError();

    try {
      _medicalCenters = await _profileService.getMedicalCenters(
        city: city,
        services: services,
      );
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل المراكز الطبية');
    } finally {
      _setLoading(false);
    }
  }

  /// Load specialists
  Future<void> loadSpecialists({
    String? specialization,
    String? medicalCenterId,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      _specialists = await _profileService.getSpecialists(
        specialization: specialization,
        medicalCenterId: medicalCenterId,
      );
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل المختصين');
    } finally {
      _setLoading(false);
    }
  }

  /// Load available cities
  Future<void> loadAvailableCities() async {
    try {
      _availableCities = await _profileService.getAvailableCities();
      notifyListeners();
    } catch (e) {
      // Use default cities if loading fails
      _availableCities = ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة'];
      notifyListeners();
    }
  }

  /// Load available services
  Future<void> loadAvailableServices() async {
    try {
      _availableServices = await _profileService.getAvailableServices();
      notifyListeners();
    } catch (e) {
      // Use default services if loading fails
      _availableServices = [
        'الأطراف الاصطناعية',
        'العلاج الفيزيائي',
        'التأهيل الحركي',
        'العلاج الوظيفي',
        'جراحة العظام'
      ];
      notifyListeners();
    }
  }

  /// Create appointment
  Future<bool> createAppointment({
    required String specialistId,
    required String medicalCenterId,
    required DateTime appointmentDate,
    required String appointmentTime,
    String? notes,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _profileService.createAppointment(
        specialistId: specialistId,
        medicalCenterId: medicalCenterId,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        notes: notes,
      );

      if (success) {
        // Reload appointments
        await loadUserAppointments();
        return true;
      } else {
        _setError('فشل في حجز الموعد');
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء حجز الموعد');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Load user appointments
  Future<void> loadUserAppointments() async {
    _setLoading(true);
    _clearError();

    try {
      _appointments = await _profileService.getUserAppointments();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل المواعيد');
    } finally {
      _setLoading(false);
    }
  }

  /// Cancel appointment
  Future<bool> cancelAppointment(String appointmentId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _profileService.cancelAppointment(appointmentId);
      if (success) {
        // Reload appointments
        await loadUserAppointments();
        return true;
      } else {
        _setError('فشل في إلغاء الموعد');
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء إلغاء الموعد');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Search for centers and specialists
  Future<Map<String, List<Map<String, dynamic>>>> search(String query) async {
    if (query.trim().isEmpty) {
      return {'centers': [], 'specialists': []};
    }

    _setLoading(true);
    _clearError();

    try {
      final results = await _profileService.search(query);
      return results;
    } catch (e) {
      _setError('فشل في البحث');
      return {'centers': [], 'specialists': []};
    } finally {
      _setLoading(false);
    }
  }

  /// Clear error message
  void clearError() {
    _clearError();
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = '';
    notifyListeners();
  }

  /// Initialize provider data
  Future<void> initialize() async {
    await Future.wait([
      loadUserProfile(),
      loadAvailableCities(),
      loadAvailableServices(),
    ]);
  }

  @override
  void dispose() {
    super.dispose();
  }
}
