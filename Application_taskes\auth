هداف جزء المصادقة (Authentication Side) – تطبيق "خطوة – Khotwa"
يهدف هذا الجزء من المشروع إلى تصميم وتطوير نظام تسجيل دخول آمن ومتكامل للمستخدمين من فئة المرضى فقط، باستخدام خدمة Supabase Auth، مع دعم اللغة العربية (RTL) وتجربة مستخدم سهلة ومتكيفة.

🟢 الشاشات الأساسية:
🎬 شاشة ترحيبية (Welcome Screen)
خلفية فيديو تفاعلية أو متكررة تعبر عن رسالة التطبيق.

أزرار واضحة:

"ابدأ"
"حول التطبيق"

📝 شاشة تسجيل حساب (Sign Up)
التسجيل مسموح فقط للمستخدمين من فئة: المرضى

الحقول المطلوبة:

الاسم الكامل

البريد الإلكتروني

كلمة المرور


✅ بعد التسجيل: إرسال المستخدم إلى صفحة التحقق من البريد الإلكتروني (عبر Supabase)

🔐 شاشة تسجيل الدخول (Sign In)
البريد الإلكتروني

كلمة المرور

رابط "نسيت كلمة المرور؟"

🧠 شاشة استعادة كلمة السر (Password Reset)
إدخال البريد الإلكتروني

إشعار للمستخدم بأن رابط إعادة التعيين تم إرساله

🛠️ قواعد العمل والتطوير:
✅ استخدام Supabase Auth لتسجيل المستخدمين الجدد وتسجيل الدخول بالبريد وكلمة السر.

🧑‍⚕️ لا يسمح بتسجيل المختصين عبر التطبيق، بل يتم إدخالهم لاحقًا من خلال لوحة تحكم الويب (Admin Dashboard).

💾 حفظ الجلسة (session) محليًا باستخدام Supabase.auth.currentSession.

📲 الانتقال التلقائي إلى الصفحة الرئيسية (HomePage) عند تسجيل الدخول.

⚠️ عرض رسائل الخطأ بشكل أنيق للمستخدم (مثلاً: البريد الإلكتروني غير مسجل – كلمة السر خاطئة…).

🌐 دعم الواجهة العربية RTL بالكامل، وخط واضح وجميل.