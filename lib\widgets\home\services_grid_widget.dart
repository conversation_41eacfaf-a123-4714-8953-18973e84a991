import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';
import '../../screens/ai_assistant_screen.dart';
import '../../screens/medical_centers_screen.dart';

/// ويدجت شبكة الخدمات في الصفحة الرئيسية
class ServicesGridWidget extends StatelessWidget {
  const ServicesGridWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Text(
            'الخدمات المتاحة',
            style: AppTextStyles.headline4.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        const SizedBox(height: 16),

        // بطاقة الذكاء الاصطناعي - صف كامل
        _buildAIAssistantCard(context),
        const SizedBox(height: 16),

        // باقي الخدمات - 3 في كل صف
        _buildServicesGrid(context),
      ],
    );
  }

  /// بطاقة المساعد الذكي - تأخذ صف كامل
  Widget _buildAIAssistantCard(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToAIAssistant(context),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // أيقونة الذكاء الاصطناعي
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.psychology,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                
                // النص والوصف
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'المساعد الذكي',
                        style: AppTextStyles.headline4.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'احصل على استشارة طبية فورية بالذكاء الاصطناعي',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // سهم التنقل
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withValues(alpha: 0.8),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// شبكة باقي الخدمات - 3 في كل صف
  Widget _buildServicesGrid(BuildContext context) {
    final services = [
      ServiceItem(
        title: 'المراكز الطبية',
        subtitle: 'ابحث عن أقرب مركز طبي',
        icon: Icons.local_hospital,
        color: Colors.red,
        onTap: () => _navigateToMedicalCenters(context),
      ),
      ServiceItem(
        title: 'الصيدليات',
        subtitle: 'اعثر على الصيدليات القريبة',
        icon: Icons.local_pharmacy,
        color: Colors.green,
        onTap: () => _showComingSoon(context, 'الصيدليات'),
      ),
      ServiceItem(
        title: 'المختبرات',
        subtitle: 'احجز موعد للتحاليل',
        icon: Icons.science,
        color: Colors.purple,
        onTap: () => _showComingSoon(context, 'المختبرات'),
      ),
      ServiceItem(
        title: 'الأطباء',
        subtitle: 'احجز موعد مع طبيب',
        icon: Icons.person_add,
        color: Colors.blue,
        onTap: () => _showComingSoon(context, 'الأطباء'),
      ),
      ServiceItem(
        title: 'الطوارئ',
        subtitle: 'أرقام الطوارئ المهمة',
        icon: Icons.emergency,
        color: Colors.orange,
        onTap: () => _navigateToEmergency(context),
      ),
      ServiceItem(
        title: 'التأمين الصحي',
        subtitle: 'معلومات التأمين',
        icon: Icons.health_and_safety,
        color: Colors.teal,
        onTap: () => _showComingSoon(context, 'التأمين الصحي'),
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.85,
      ),
      itemCount: services.length,
      itemBuilder: (context, index) {
        final service = services[index];
        return _buildServiceCard(service);
      },
    );
  }

  /// بطاقة خدمة واحدة
  Widget _buildServiceCard(ServiceItem service) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: service.onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة الخدمة
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: service.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    service.icon,
                    color: service.color,
                    size: 24,
                  ),
                ),
                const SizedBox(height: 8),
                
                // عنوان الخدمة
                Text(
                  service.title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                
                // وصف الخدمة
                Text(
                  service.subtitle,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // دوال التنقل
  void _navigateToAIAssistant(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AIAssistantScreen()),
    );
  }

  void _navigateToMedicalCenters(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const MedicalCentersScreen()),
    );
  }

  void _navigateToEmergency(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const MedicalCentersScreen(initialTab: 2),
      ),
    );
  }

  void _showComingSoon(BuildContext context, String serviceName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('خدمة $serviceName قريباً...'),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// نموذج عنصر الخدمة
class ServiceItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const ServiceItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
