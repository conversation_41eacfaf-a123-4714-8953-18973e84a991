import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../theme/app_styles.dart';
import '../theme/app_icons.dart';
import '../config/app_config.dart';
import '../providers/auth_provider.dart';

/// Forgot Password Screen - Password reset functionality
/// Allows users to reset their password via email
/// Supports RTL layout and proper validation

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isEmailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              AppIcons.getBackIcon(TextDirection.rtl),
              color: AppColors.textPrimary,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.paddingLarge),
            child: _isEmailSent ? _buildSuccessView() : _buildFormView(),
          ),
        ),
      ),
    );
  }

  Widget _buildFormView() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: AppDimensions.marginXLarge),
          _buildEmailField(),
          const SizedBox(height: AppDimensions.marginXLarge),
          _buildResetButton(),
          const SizedBox(height: AppDimensions.marginLarge),
          _buildErrorMessage(),
          const SizedBox(height: AppDimensions.marginLarge),
          _buildBackToSignInLink(),
        ],
      ),
    );
  }

  Widget _buildSuccessView() {
    return Column(
      children: [
        const SizedBox(height: AppDimensions.marginXLarge * 2),
        _buildSuccessIcon(),
        const SizedBox(height: AppDimensions.marginXLarge),
        _buildSuccessMessage(),
        const SizedBox(height: AppDimensions.marginXLarge),
        _buildInstructions(),
        const SizedBox(height: AppDimensions.marginXLarge),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppConfig.titleForgotPassword,
          style: AppTextStyles.headline2.copyWith(
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Text(
          'أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildEmailField() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return TextFormField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.done,
          style: AppTextStyles.inputText,
          decoration: InputDecoration(
            labelText: AppConfig.labelEmail,
            labelStyle: AppTextStyles.inputLabel,
            hintText: AppConfig.placeholderEmail,
            hintStyle: AppTextStyles.inputHint,
            prefixIcon: Icon(
              AppIcons.email,
              color: AppColors.textSecondary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              borderSide: const BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              borderSide: const BorderSide(color: AppColors.error, width: 2),
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          validator: authProvider.validateEmail,
          onFieldSubmitted: (_) => _handleResetPassword(),
        );
      },
    );
  }

  Widget _buildResetButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeightLarge,
          child: ElevatedButton(
            onPressed: authProvider.isLoading ? null : _handleResetPassword,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
              elevation: AppDimensions.elevationMedium,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
            ),
            child: authProvider.isLoading
                ? const SpinKitThreeBounce(
                    color: AppColors.textOnPrimary,
                    size: 20,
                  )
                : Text(
                    AppConfig.buttonResetPassword,
                    style: AppTextStyles.buttonLarge,
                  ),
          ),
        );
      },
    );
  }

  Widget _buildErrorMessage() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.errorMessage.isEmpty) {
          return const SizedBox.shrink();
        }
        
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppDimensions.paddingMedium),
          decoration: BoxDecoration(
            color: AppColors.error.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            border: Border.all(color: AppColors.error.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(
                AppIcons.error,
                color: AppColors.error,
                size: AppDimensions.iconMedium,
              ),
              const SizedBox(width: AppDimensions.marginSmall),
              Expanded(
                child: Text(
                  authProvider.errorMessage,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.error,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBackToSignInLink() {
    return Center(
      child: TextButton(
        onPressed: () => Navigator.of(context).pop(),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              AppIcons.getBackIcon(TextDirection.rtl),
              size: AppDimensions.iconSmall,
              color: AppColors.primary,
            ),
            const SizedBox(width: AppDimensions.marginSmall),
            Text(
              'العودة إلى تسجيل الدخول',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: AppFonts.medium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessIcon() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusCircular),
        border: Border.all(
          color: AppColors.success.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: const Icon(
        Icons.mark_email_read,
        size: 60,
        color: AppColors.success,
      ),
    );
  }

  Widget _buildSuccessMessage() {
    return Column(
      children: [
        Text(
          'تم إرسال الرابط!',
          style: AppTextStyles.headline3.copyWith(
            color: AppColors.success,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Text(
          AppConfig.successPasswordResetSent,
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInstructions() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.info.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.info.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                AppIcons.info,
                color: AppColors.info,
                size: AppDimensions.iconMedium,
              ),
              const SizedBox(width: AppDimensions.marginSmall),
              Text(
                'تعليمات مهمة',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.info,
                  fontWeight: AppFonts.semiBold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.marginMedium),
          Text(
            '• تحقق من صندوق الوارد في بريدك الإلكتروني\n'
            '• قد يصل الرابط خلال بضع دقائق\n'
            '• تحقق من مجلد الرسائل غير المرغوب فيها\n'
            '• الرابط صالح لمدة 24 ساعة فقط',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.info,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
            ),
            child: Text(
              'العودة إلى تسجيل الدخول',
              style: AppTextStyles.buttonMedium,
            ),
          ),
        ),
        const SizedBox(height: AppDimensions.marginMedium),
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: OutlinedButton(
            onPressed: _handleResendEmail,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
            ),
            child: Text(
              'إعادة إرسال الرابط',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _handleResetPassword() async {
    // Clear any previous errors
    context.read<AuthProvider>().clearError();
    
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final authProvider = context.read<AuthProvider>();
    final success = await authProvider.resetPassword(
      email: _emailController.text.trim(),
    );

    if (success && mounted) {
      setState(() {
        _isEmailSent = true;
      });
    }
  }

  void _handleResendEmail() async {
    final authProvider = context.read<AuthProvider>();
    await authProvider.resetPassword(
      email: _emailController.text.trim(),
    );
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم إعادة إرسال الرابط'),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
          ),
        ),
      );
    }
  }
}
