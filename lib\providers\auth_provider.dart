import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../data/auth_service.dart';
import '../data/medical_profile_service.dart';
import '../config/app_config.dart';

/// Authentication Provider for State Management
/// Manages authentication state throughout the app
/// Uses Provider pattern for reactive UI updates

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final MedicalProfileService _medicalProfileService = MedicalProfileService();

  // Authentication state
  bool _isLoading = false;
  bool _isInitialized = false;
  String _errorMessage = '';
  User? _currentUser;
  bool _isProfileComplete = false;

  // Getters
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  bool get isAuthenticated => _currentUser != null;
  bool get isEmailVerified => _authService.isEmailVerified;
  String get errorMessage => _errorMessage;
  User? get currentUser => _currentUser;
  String get userDisplayName => _authService.userDisplayName;
  bool get isProfileComplete => _isProfileComplete;

  /// Initialize the authentication provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🔐 Initializing AuthProvider...');
    _setLoading(true);

    try {
      await _authService.initialize();
      debugPrint('✅ AuthService initialized');
      _currentUser = _authService.currentUser;
      debugPrint('👤 Current user: ${_currentUser?.email ?? 'None'}');

      // Listen to auth state changes
      _authService.authStateChanges.listen((AuthState data) {
        debugPrint(
          '🔄 Auth state changed: ${data.session?.user?.email ?? 'None'}',
        );
        _currentUser = data.session?.user;
        notifyListeners();
      });

      _isInitialized = true;
      debugPrint('✅ AuthProvider initialization complete');
      _clearError();
    } catch (e) {
      _setError('فشل في تهيئة نظام المصادقة');
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up new patient
  Future<bool> signUpPatient({
    required String fullName,
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.signUpPatient(
        fullName: fullName,
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        _currentUser = result.user;
        notifyListeners();
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError(AppConfig.errorUnknown);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign in existing user
  Future<bool> signIn({required String email, required String password}) async {
    _setLoading(true);
    _clearError();

    try {
      debugPrint('🔐 Attempting to sign in user: $email');
      final result = await _authService.signIn(
        email: email,
        password: password,
      );

      debugPrint('🔐 Sign in result: ${result.isSuccess}');
      if (result.isSuccess) {
        _currentUser = result.user;
        debugPrint('👤 User signed in: ${_currentUser?.email}');

        // Check profile completion after successful sign in
        await _checkProfileCompletion();
        debugPrint('📋 Profile complete: $_isProfileComplete');

        notifyListeners();
        return true;
      } else {
        debugPrint('❌ Sign in failed: ${result.message}');
        _setError(result.message);
        return false;
      }
    } catch (e) {
      debugPrint('💥 Sign in exception: $e');
      _setError('حدث خطأ غير متوقع: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    _setLoading(true);

    try {
      await _authService.signOut();
      _currentUser = null;
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تسجيل الخروج');
    } finally {
      _setLoading(false);
    }
  }

  /// Reset password
  Future<bool> resetPassword({required String email}) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.resetPassword(email: email);

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError(AppConfig.errorUnknown);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Resend email verification
  Future<bool> resendEmailVerification() async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.resendEmailVerification();

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError(AppConfig.errorUnknown);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Check if user has valid session
  Future<bool> checkSession() async {
    try {
      final hasValidSession = await _authService.hasValidSession();
      if (hasValidSession && _authService.isAuthenticated) {
        _currentUser = _authService.currentUser;
        // Check if medical profile is complete
        await _checkProfileCompletion();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Check if medical profile is complete
  Future<void> _checkProfileCompletion() async {
    try {
      debugPrint('📋 Checking profile completion...');
      _isProfileComplete = await _medicalProfileService.isProfileComplete();
      debugPrint('📋 Profile completion result: $_isProfileComplete');
    } catch (e) {
      debugPrint('❌ Error checking profile completion: $e');
      // If there's an error checking profile completion, assume it's not complete
      _isProfileComplete = false;
    }
  }

  /// Update profile completion status (call this after completing profile)
  void updateProfileCompletionStatus(bool isComplete) {
    _isProfileComplete = isComplete;
    notifyListeners();
  }

  /// Validate form inputs
  String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    if (!AppConfig.isValidEmail(email)) {
      return AppConfig.errorInvalidEmail;
    }
    return null;
  }

  String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    if (password.length < AppConfig.minPasswordLength) {
      return AppConfig.errorPasswordTooShort;
    }
    if (password.length > AppConfig.maxPasswordLength) {
      return 'كلمة المرور طويلة جداً';
    }
    return null;
  }

  String? validateFullName(String? name) {
    if (name == null || name.isEmpty) {
      return 'الاسم الكامل مطلوب';
    }
    if (name.length < AppConfig.minNameLength) {
      return AppConfig.errorNameTooShort;
    }
    if (name.length > AppConfig.maxNameLength) {
      return AppConfig.errorNameTooLong;
    }
    return null;
  }

  String? validateConfirmPassword(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    if (password != confirmPassword) {
      return 'كلمة المرور غير متطابقة';
    }
    return null;
  }

  /// Clear error message
  void clearError() {
    _clearError();
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = '';
    notifyListeners();
  }

  /// Get user metadata
  Map<String, dynamic>? get userMetadata => _currentUser?.userMetadata;

  /// Get user type
  String get userType =>
      userMetadata?['user_type'] ?? AppConfig.userTypePatient;

  /// Check if user is patient
  bool get isPatient => userType == AppConfig.userTypePatient;

  /// Get user creation date
  DateTime? get userCreatedAt {
    final createdAtString = userMetadata?['created_at'];
    if (createdAtString != null && createdAtString is String) {
      return DateTime.tryParse(createdAtString);
    }
    // Handle Supabase User createdAt which might be String or DateTime
    final userCreatedAt = _currentUser?.createdAt;
    if (userCreatedAt is String) {
      return DateTime.tryParse(userCreatedAt);
    }
    return userCreatedAt as DateTime?;
  }

  /// Get user email
  String get userEmail => _currentUser?.email ?? '';

  /// Get user ID
  String get userId => _currentUser?.id ?? '';

  @override
  void dispose() {
    // Clean up any resources if needed
    super.dispose();
  }
}
