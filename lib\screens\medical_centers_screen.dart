import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../theme/app_styles.dart';
import '../models/medical_center_model.dart';
import '../services/medical_centers_service.dart';
import '../widgets/medical_centers/medical_center_card.dart';
import '../widgets/medical_centers/emergency_contacts_widget.dart';
import '../widgets/medical_centers/medical_centers_map_widget.dart';

/// صفحة المراكز الطبية
class MedicalCentersScreen extends StatefulWidget {
  const MedicalCentersScreen({super.key});

  @override
  State<MedicalCentersScreen> createState() => _MedicalCentersScreenState();
}

class _MedicalCentersScreenState extends State<MedicalCentersScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<MedicalCenter> _medicalCenters = [];
  Position? _userPosition;
  bool _isLoading = true;
  String? _errorMessage;
  String _selectedType = 'all';

  final List<String> _centerTypes = [
    'all',
    'hospital',
    'clinic',
    'pharmacy',
    'laboratory',
    'emergency',
  ];

  final Map<String, String> _typeNames = {
    'all': 'الكل',
    'hospital': 'المستشفيات',
    'clinic': 'العيادات',
    'pharmacy': 'الصيدليات',
    'laboratory': 'المختبرات',
    'emergency': 'الطوارئ',
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeLocation();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeLocation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // التحقق من صلاحيات الموقع
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw 'تم رفض صلاحيات الموقع';
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw 'تم رفض صلاحيات الموقع نهائياً. يرجى تفعيلها من الإعدادات';
      }

      // الحصول على الموقع الحالي
      _userPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      await _loadMedicalCenters();
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
      // تحميل البيانات بدون موقع
      await _loadMedicalCenters();
    }
  }

  Future<void> _loadMedicalCenters() async {
    try {
      final centers = await MedicalCentersService.getNearbyMedicalCenters(
        userPosition: _userPosition,
        type: _selectedType == 'all' ? null : _selectedType,
      );

      setState(() {
        _medicalCenters = centers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildCentersListTab(),
                  _buildMapTab(),
                  _buildEmergencyTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.primary,
      elevation: 0,
      title: Text(
        'المراكز الطبية',
        style: AppTextStyles.headline4.copyWith(
          color: AppColors.textOnPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(
          Icons.arrow_back_ios,
          color: AppColors.textOnPrimary,
        ),
      ),
      actions: [
        IconButton(
          onPressed: _initializeLocation,
          icon: Icon(
            Icons.refresh,
            color: AppColors.textOnPrimary,
          ),
          tooltip: 'تحديث الموقع',
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(
            icon: Icon(Icons.list),
            text: 'القائمة',
          ),
          Tab(
            icon: Icon(Icons.map),
            text: 'الخريطة',
          ),
          Tab(
            icon: Icon(Icons.emergency),
            text: 'الطوارئ',
          ),
        ],
      ),
    );
  }

  Widget _buildCentersListTab() {
    return Column(
      children: [
        _buildTypeFilter(),
        Expanded(
          child: _isLoading
              ? _buildLoadingWidget()
              : _errorMessage != null
                  ? _buildErrorWidget()
                  : _medicalCenters.isEmpty
                      ? _buildEmptyWidget()
                      : _buildCentersList(),
        ),
      ],
    );
  }

  Widget _buildTypeFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: _centerTypes.map((type) {
            final isSelected = _selectedType == type;
            return Padding(
              padding: const EdgeInsets.only(left: 8),
              child: FilterChip(
                label: Text(_typeNames[type] ?? type),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedType = type;
                  });
                  _loadMedicalCenters();
                },
                backgroundColor: AppColors.surface,
                selectedColor: AppColors.primary.withValues(alpha: 0.2),
                labelStyle: AppTextStyles.bodyMedium.copyWith(
                  color: isSelected ? AppColors.primary : AppColors.textSecondary,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _initializeLocation,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مراكز طبية في المنطقة المحددة',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCentersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _medicalCenters.length,
      itemBuilder: (context, index) {
        final center = _medicalCenters[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: MedicalCenterCard(
            center: center,
            userPosition: _userPosition,
          ),
        );
      },
    );
  }

  Widget _buildMapTab() {
    return MedicalCentersMapWidget(
      medicalCenters: _medicalCenters,
      userPosition: _userPosition,
      isLoading: _isLoading,
    );
  }

  Widget _buildEmergencyTab() {
    return const EmergencyContactsWidget();
  }
}
