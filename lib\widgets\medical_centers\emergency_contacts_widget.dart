import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';
import '../../models/medical_center_model.dart';
import '../../services/medical_centers_service.dart';

/// ويدجت جهات اتصال الطوارئ
class EmergencyContactsWidget extends StatelessWidget {
  const EmergencyContactsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildWarningCard(),
          const SizedBox(height: 24),
          Expanded(child: _buildEmergencyContacts(context)),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(Icons.emergency, color: Colors.red, size: 28),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'أرقام الطوارئ',
                style: AppTextStyles.headline4.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'للحالات الطارئة فقط',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildWarningCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(Icons.warning_amber_rounded, color: Colors.red, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تنبيه مهم',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'استخدم هذه الأرقام في الحالات الطارئة فقط. في حالة عدم الطوارئ، يرجى زيارة أقرب مركز طبي.',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.red.shade700,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencyContacts(BuildContext context) {
    // استخدام أرقام الطوارئ الجزائرية
    final algerianEmergencyNumbers = [
      const EmergencyContact(
        name: 'الحماية المدنية',
        number: '14',
        iconData: Icons.local_fire_department,
        description: 'خدمة الحماية المدنية والإطفاء',
      ),
      const EmergencyContact(
        name: 'الشرطة',
        number: '17',
        iconData: Icons.local_police,
        description: 'خدمة الشرطة والأمن العام',
      ),
      const EmergencyContact(
        name: 'الدرك الوطني',
        number: '1055',
        iconData: Icons.security,
        description: 'خدمة الدرك الوطني',
      ),
      const EmergencyContact(
        name: 'الإسعاف',
        number: '15',
        iconData: Icons.local_hospital,
        description: 'خدمة الإسعاف والطوارئ الطبية',
      ),
    ];

    return ListView.builder(
      itemCount: algerianEmergencyNumbers.length,
      itemBuilder: (context, index) {
        final contact = algerianEmergencyNumbers[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildEmergencyContactCard(context, contact),
        );
      },
    );
  }

  Widget _buildEmergencyContactCard(
    BuildContext context,
    EmergencyContact contact,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showCallConfirmation(context, contact),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: _getContactColor(contact.name).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  contact.iconData,
                  color: _getContactColor(contact.name),
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      contact.name,
                      style: AppTextStyles.headline4.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      contact.description,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getContactColor(
                          contact.name,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        contact.number,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: _getContactColor(contact.name),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getContactColor(contact.name),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.phone, color: Colors.white, size: 24),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getContactColor(String contactName) {
    switch (contactName) {
      case 'الإسعاف':
        return Colors.red;
      case 'الشرطة':
        return Colors.blue;
      case 'الإطفاء':
        return Colors.orange;
      case 'مركز السموم':
        return Colors.purple;
      default:
        return AppColors.primary;
    }
  }

  void _showCallConfirmation(BuildContext context, EmergencyContact contact) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              contact.iconData,
              color: _getContactColor(contact.name),
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              'الاتصال بـ ${contact.name}',
              style: AppTextStyles.headline4.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل تريد الاتصال بـ ${contact.name}؟',
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.phone,
                    color: _getContactColor(contact.name),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    contact.number,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _getContactColor(contact.name),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              contact.description,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إلغاء',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _makeEmergencyCall(context, contact);
            },
            icon: const Icon(Icons.phone, size: 18),
            label: const Text('اتصال'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getContactColor(contact.name),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _makeEmergencyCall(
    BuildContext context,
    EmergencyContact contact,
  ) async {
    try {
      await MedicalCentersService.makePhoneCall(contact.number);
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
