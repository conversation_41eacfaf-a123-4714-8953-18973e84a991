import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/medical_profile_provider.dart';
import '../../theme/app_styles.dart';

/// Step 6: Final Consent
/// Final step with privacy consent and completion
class Step6FinalConsent extends StatefulWidget {
  const Step6FinalConsent({super.key});

  @override
  State<Step6FinalConsent> createState() => _Step6FinalConsentState();
}

class _Step6FinalConsentState extends State<Step6FinalConsent> {
  bool _privacyConsent = false;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    final provider = context.read<MedicalProfileProvider>();
    final profile = provider.profile;
    _privacyConsent = profile.privacyConsent;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSuccessCard(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildSummaryCard(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildPrivacyConsent(),
            const SizedBox(height: AppDimensions.marginXLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessCard() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: AppColors.success,
            size: AppDimensions.iconLarge,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أوشكت على الانتهاء!',
                  style: AppTextStyles.headline4.copyWith(
                    color: AppColors.success,
                  ),
                ),
                const SizedBox(height: AppDimensions.marginSmall),
                Text(
                  'لقد أكملت جميع المعلومات المطلوبة لملفك الطبي.',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final provider = context.read<MedicalProfileProvider>();
    final profile = provider.profile;
    final completionPercentage = provider.getCompletionPercentage();

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.summarize_outlined,
                color: AppColors.primary,
                size: AppDimensions.iconMedium,
              ),
              const SizedBox(width: AppDimensions.marginMedium),
              Text(
                'ملخص الملف الطبي',
                style: AppTextStyles.headline4.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.marginLarge),
          
          // Completion progress
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: completionPercentage,
                  backgroundColor: AppColors.border,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.success),
                  minHeight: 8,
                ),
              ),
              const SizedBox(width: AppDimensions.marginMedium),
              Text(
                '${(completionPercentage * 100).round()}%',
                style: AppTextStyles.labelMedium.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.marginLarge),
          
          // Summary items
          _buildSummaryItem(
            icon: Icons.person_outline,
            title: 'المعلومات الشخصية',
            value: profile.fullName ?? 'غير محدد',
            isComplete: profile.isStep1Valid(),
          ),
          _buildSummaryItem(
            icon: Icons.medical_information_outlined,
            title: 'الحالة الطبية',
            value: profile.disabilityType ?? 'غير محدد',
            isComplete: profile.isStep2Valid(),
          ),
          _buildSummaryItem(
            icon: Icons.accessibility_new_outlined,
            title: 'الطرف الاصطناعي',
            value: profile.usesProsthetic ? 'يستخدم' : 'لا يستخدم',
            isComplete: profile.isStep3Valid(),
          ),
          _buildSummaryItem(
            icon: Icons.location_on_outlined,
            title: 'الموقع',
            value: profile.city != null ? '${profile.city}, ${profile.state}' : 'غير محدد',
            isComplete: profile.isStep5Valid(),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem({
    required IconData icon,
    required String title,
    required String value,
    required bool isComplete,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.marginMedium),
      child: Row(
        children: [
          Icon(
            icon,
            color: isComplete ? AppColors.success : AppColors.textSecondary,
            size: AppDimensions.iconSmall,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  value,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            isComplete ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isComplete ? AppColors.success : AppColors.textSecondary,
            size: AppDimensions.iconSmall,
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacyConsent() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.privacy_tip_outlined,
                color: AppColors.primary,
                size: AppDimensions.iconMedium,
              ),
              const SizedBox(width: AppDimensions.marginMedium),
              Text(
                'الخصوصية والموافقة',
                style: AppTextStyles.headline4.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.marginLarge),
          
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
            ),
            child: Text(
              'نحن نحترم خصوصيتك ونلتزم بحماية معلوماتك الطبية. '
              'ستُستخدم هذه المعلومات فقط لتقديم أفضل الخدمات الطبية لك '
              'ولن تُشارك مع أي جهة خارجية دون موافقتك الصريحة.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.info,
                height: 1.5,
              ),
            ),
          ),
          
          const SizedBox(height: AppDimensions.marginLarge),
          
          GestureDetector(
            onTap: () {
              setState(() {
                _privacyConsent = !_privacyConsent;
              });
              _updateConsent();
            },
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: _privacyConsent ? AppColors.primary : Colors.transparent,
                    border: Border.all(
                      color: _privacyConsent ? AppColors.primary : AppColors.border,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: _privacyConsent
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
                const SizedBox(width: AppDimensions.marginMedium),
                Expanded(
                  child: Text(
                    'أوافق على شروط الخصوصية واستخدام معلوماتي الطبية '
                    'لتقديم الخدمات الطبية والاستشارات المناسبة.',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textPrimary,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          if (!_privacyConsent)
            Padding(
              padding: const EdgeInsets.only(top: AppDimensions.marginSmall),
              child: Text(
                'يجب الموافقة على شروط الخصوصية لإكمال الملف الطبي',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _updateConsent() {
    final provider = context.read<MedicalProfileProvider>();
    provider.updateConsent(privacyConsent: _privacyConsent);
  }
}
