import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/chat_message_model.dart';

/// خدمة المساعد الذكي باستخدام OpenRouter API
class AIAssistantService {
  static const String _baseUrl = 'https://openrouter.ai/api/v1';
  static const String _apiKey = '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';
  static const String _model = 'deepseek/deepseek-chat-v3-0324:free';
  
  static const String _systemPrompt = '''
أنت مساعد ذكي طبي متخصص في تطبيق "خطوة" للرعاية الصحية.

مهامك:
1. تقديم المشورة الطبية العامة والإرشادات الصحية
2. مساعدة المستخدمين في فهم الأعراض والحالات الصحية
3. تقديم نصائح للوقاية والعلاج الأولي
4. إرشاد المستخدمين لطلب المساعدة الطبية المتخصصة عند الحاجة

قواعد مهمة:
- تحدث باللغة العربية دائماً
- كن ودوداً ومتفهماً
- لا تقدم تشخيصات طبية نهائية
- انصح بزيارة الطبيب في الحالات الجدية
- قدم معلومات دقيقة ومفيدة
- اجعل إجاباتك واضحة ومفهومة

ابدأ محادثاتك بترحيب ودود واسأل كيف يمكنك المساعدة.
''';

  /// إرسال رسالة للمساعد الذكي
  static Future<String> sendMessage(
    String message,
    List<ChatMessage> conversationHistory,
  ) async {
    try {
      // تحضير رسائل المحادثة
      final messages = _prepareMessages(message, conversationHistory);
      
      // إعداد الطلب
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://khotwa-app.com',
          'X-Title': 'Khotwa Medical Assistant',
        },
        body: jsonEncode({
          'model': _model,
          'messages': messages,
          'max_tokens': 1000,
          'temperature': 0.7,
          'top_p': 0.9,
          'frequency_penalty': 0.1,
          'presence_penalty': 0.1,
          'stream': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        return content?.toString() ?? 'عذراً، لم أتمكن من فهم طلبك. يرجى المحاولة مرة أخرى.';
      } else {
        debugPrint('AI API Error: ${response.statusCode} - ${response.body}');
        return _getErrorMessage(response.statusCode);
      }
    } catch (e) {
      debugPrint('AI Service Error: $e');
      return 'عذراً، حدث خطأ في الاتصال. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';
    }
  }

  /// تحضير رسائل المحادثة للإرسال
  static List<Map<String, String>> _prepareMessages(
    String currentMessage,
    List<ChatMessage> history,
  ) {
    final messages = <Map<String, String>>[
      {'role': 'system', 'content': _systemPrompt},
    ];

    // إضافة آخر 10 رسائل من التاريخ للحفاظ على السياق
    final recentHistory = history.length > 10 
        ? history.sublist(history.length - 10)
        : history;

    for (final msg in recentHistory) {
      messages.add({
        'role': msg.isUser ? 'user' : 'assistant',
        'content': msg.content,
      });
    }

    // إضافة الرسالة الحالية
    messages.add({
      'role': 'user',
      'content': currentMessage,
    });

    return messages;
  }

  /// الحصول على رسالة خطأ مناسبة
  static String _getErrorMessage(int statusCode) {
    switch (statusCode) {
      case 401:
        return 'خطأ في المصادقة. يرجى المحاولة لاحقاً.';
      case 429:
        return 'تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار قليلاً.';
      case 500:
        return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
      default:
        return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }
  }

  /// اقتراحات سريعة للبدء
  static List<String> getWelcomeQuestions() {
    return [
      'كيف يمكنني تحسين صحتي العامة؟',
      'ما هي النصائح للحفاظ على نظام غذائي صحي؟',
      'كيف أتعامل مع التوتر والقلق؟',
      'ما هي أهمية ممارسة الرياضة؟',
    ];
  }

  /// رسالة ترحيب افتراضية
  static String getWelcomeMessage() {
    return '''مرحباً بك في مساعد خطوة الذكي! 👋

أنا هنا لمساعدتك في جميع استفساراتك الصحية والطبية. يمكنني:

🩺 تقديم المشورة الطبية العامة
💊 شرح الأدوية والعلاجات
📅 مساعدتك في فهم الأعراض
🏥 إرشادك لطلب المساعدة المتخصصة

كيف يمكنني مساعدتك اليوم؟''';
  }
}
