import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';

/// المساعد الذكي - Smart Assistant Card
///
/// أهم مكون في الصفحة الرئيسية - مركز الذكاء الاصطناعي
/// يتيح للمستخدم طرح أسئلة والحصول على نصائح ذكية
class SmartAssistantCard extends StatelessWidget {
  const SmartAssistantCard({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _handleAssistantTap(context),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppDimensions.paddingLarge),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        child: Row(
          children: [
            // أيقونة بسيطة
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.textOnPrimary.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
              ),
              child: Icon(
                Icons.smart_toy_outlined,
                color: AppColors.textOnPrimary,
                size: 24,
              ),
            ),

            const SizedBox(width: AppDimensions.marginMedium),

            // النص
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المساعد الذكي',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textOnPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'اسأل أي سؤال حول حالتك الصحية',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),

            // سهم
            Icon(
              Icons.arrow_back_ios_new,
              color: AppColors.textOnPrimary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _handleAssistantTap(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('سيتم فتح المساعد الذكي قريباً...'),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
      ),
    );
  }
}
